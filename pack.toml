[package]
name = "adversarial_robustness"
version = "0.1.0"
authors = ["<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>"]
description = "Adversarial robustness verification using dependent types in Idris vs empirical testing in Python"
readme = "README.md"
license = "MIT"
keywords = ["machine-learning", "dependent-types", "adversarial-robustness", "formal-verification"]
categories = ["machine-learning", "verification", "research"]

[dependencies]
base = ">= 0.5.1"
contrib = ">= 0.5.1"
network = ">= 0.5.1"
json = ">= 0.1.0"
spidr = ">= 0.0.1" 

