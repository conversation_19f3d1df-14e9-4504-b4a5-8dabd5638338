[package]
name = "adversarial_robustness"
version = "0.1.0"
authors = ["<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>"]
description = "Adversarial robustness verification using dependent types in Idris vs empirical testing in Python"
readme = "README.md"
homepage = "https://github.com/your-username/adversarial-robustness-verification"
repository = "https://github.com/your-username/adversarial-robustness-verification.git"
license = "MIT"
keywords = ["machine-learning", "dependent-types", "adversarial-robustness", "formal-verification"]
categories = ["machine-learning", "verification", "research"]

# Project metadata for dissertation
[package.metadata]
project = "Accelerated Machine Learning with Dependent Types"
subproject = "Project 5: Adversarial Robustness Verification"
institution = "University of St Andrews"
supervisor = "<PERSON>"
secondary_supervisor = "<PERSON>"
student = "Rishabh Ashok Patil"
student_id = "230029895"
academic_year = "2024-2025"

# Custom package configuration for adversarial robustness project
[custom.all.adversarial_robustness]
type = "local"
path = "."
ipkg = "adversarial_robustness.ipkg"

# Dependencies configuration
[custom.all.adversarial_robustness.depends]
base = ">= 0.5.1"
contrib = ">= 0.5.1"
network = ">= 0.5.1"  
json = ">= 0.1.0"
spidr = ">= 0.1.0"

# Spidr library configuration for tensor operations
# Note: This assumes Spidr is available in the custom pack edition
[custom.all.spidr]
type = "github"
url = "https://github.com/spidr-org/spidr"
commit = "main"  # or specific commit hash if needed
ipkg = "spidr.ipkg"

[custom.all.spidr.depends]
base = ">= 0.5.1"
contrib = ">= 0.5.1"

# Tensor library configuration (part of Spidr ecosystem)
[custom.all.tensor]
type = "github" 
url = "https://github.com/spidr-org/tensor"
commit = "main"  # or specific commit hash if needed
ipkg = "tensor.ipkg"

[custom.all.tensor.depends]
base = ">= 0.5.1"
contrib = ">= 0.5.1"

# JSON library for data interchange with Python
[custom.all.json]
type = "github"
url = "https://github.com/stefan-hoeck/idris2-json"
commit = "main"
ipkg = "json.ipkg"

[custom.all.json.depends]
base = ">= 0.5.1"
contrib = ">= 0.5.1"

# Build configuration
[build]
# Custom build options for tensor computations
opts = ["--no-banner", "--check"]
# Enable optimizations for mathematical computations
codegen = "chez"  # Use Chez Scheme backend for performance

# Spidr-specific build configuration
[build.spidr]
# XLA integration settings (if available in custom pack)
xla_enabled = true
xla_backend = "cpu"  # or "gpu" if CUDA available
optimization_level = 2

# Experimental features for advanced type checking
experimental_features = [
    "linear_types",      # For resource-aware programming
    "multiplicities",    # For fine-grained control
    "elaborator_reflection"  # For proof automation
]

# Development configuration
[dev]
# Enable extra checks during development
extra_checks = true
verbose_errors = true
show_machine_names = true

# Testing configuration
[test]
# Test framework configuration if needed
framework = "hedgehog"  # Property-based testing
timeout = 300  # 5 minutes timeout for complex proofs

# Documentation generation
[docs]
# Generate documentation for the formal verification components
include_private = false
include_source_links = true
output_format = "html"

# Custom pack configuration for dissertation project
[custom.config]
# Special configuration for academic/research use
academic_mode = true
enable_proof_logging = true
type_checking_verbosity = "detailed"

# Integration with Python components
python_interop = true
data_exchange_format = "json"
shared_data_directory = "data"
results_directory = "results"

# Performance monitoring
[performance]
# Track compilation and execution performance
track_compile_time = true
track_memory_usage = true
benchmark_mode = false

# Research-specific settings
[research]
# Enable features useful for dissertation research
formal_verification_mode = true
comparison_logging = true
metrics_collection = true

# Export configuration for integration with Python pipeline
export_results = true
export_format = ["json", "csv"]
export_directory = "results"

# Version constraints for reproducibility
[constraints]
# Lock versions for consistent research results
idris2 = "0.6.0"
pack = ">= 0.3.0"
spidr = ">= 0.1.0"

# Platform-specific configuration
[target.macos]
# macOS-specific optimizations
native_optimization = true
accelerate_framework = true  # Use macOS Accelerate framework if available

[target.linux] 
# Linux-specific optimizations
blas_backend = "openblas"
lapack_backend = "openblas"

[target.windows]
# Windows-specific configuration
blas_backend = "mkl"  # Intel MKL if available