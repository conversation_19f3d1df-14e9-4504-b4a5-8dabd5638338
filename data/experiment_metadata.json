{"experiment_info": {"dataset": "MNIST", "architecture": "[784, 256, 128, 10]", "training_samples": 10000, "test_samples": 1000, "framework": "PyTorch", "timestamp": "2025-07-23 18:44:31"}, "training_results": {"epochs": 5, "final_training_accuracy": 97.94, "training_time_seconds": 13.74758505821228, "average_epoch_time": 2.7494929313659666, "final_loss": 0.06687835175662664}, "test_results": {"test_accuracy": 94.2, "evaluation_time_seconds": 0.23234891891479492, "correct_predictions": 942, "total_test_samples": 1000, "class_accuracies": {"class_0": 98.82352941176471, "class_1": 99.2063492063492, "class_2": 94.82758620689656, "class_3": 91.58878504672897, "class_4": 96.36363636363636, "class_5": 87.35632183908046, "class_6": 94.25287356321839, "class_7": 95.95959595959596, "class_8": 94.38202247191012, "class_9": 87.23404255319149}}, "model_info": {"total_parameters": null, "model_size_mb": null, "architecture_depth": 3, "activation_function": "relu"}}