#!/usr/bin/env python3
"""
Setup Validation Script
=======================

Quick validation to ensure the adversarial robustness verification project
environment is properly configured before running experiments.

Usage: python3 validate_setup.py
"""

import os
import sys
import platform
import subprocess
import importlib
import json
import time
from typing import Dict, List, Tuple, Any, Optional

class Color:
    """ANSI color codes for terminal output."""
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    PURPLE = '\033[0;35m'
    CYAN = '\033[0;36m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    END = '\033[0m'

class SetupValidator:
    """Comprehensive setup validation for the adversarial robustness project."""
    
    def __init__(self):
        self.total_checks = 0
        self.passed_checks = 0
        self.warnings = []
        self.errors = []
        self.start_time = time.time()
    
    def print_header(self, text: str):
        """Print a formatted header."""
        print(f"\n{Color.BLUE}{'='*60}{Color.END}")
        print(f"{Color.BLUE}{Color.BOLD}{text}{Color.END}")
        print(f"{Color.BLUE}{'='*60}{Color.END}")
    
    def print_step(self, text: str):
        """Print a step indicator."""
        print(f"\n{Color.GREEN}[CHECK]{Color.END} {text}")
    
    def print_substep(self, text: str):
        """Print a substep indicator."""
        print(f"{Color.PURPLE}  →{Color.END} {text}")
    
    def print_success(self, text: str):
        """Print a success message."""
        print(f"{Color.GREEN}✓ {text}{Color.END}")
    
    def print_warning(self, text: str):
        """Print a warning message."""
        print(f"{Color.YELLOW}⚠ {text}{Color.END}")
        self.warnings.append(text)
    
    def print_error(self, text: str):
        """Print an error message."""
        print(f"{Color.RED}✗ {text}{Color.END}")
        self.errors.append(text)
    
    def check_system_info(self) -> bool:
        """Check basic system information."""
        self.print_step("System Information")
        self.total_checks += 1
        
        try:
            # Operating system
            system = platform.system()
            release = platform.release()
            machine = platform.machine()
            python_version = platform.python_version()
            
            self.print_substep(f"OS: {system} {release}")
            self.print_substep(f"Architecture: {machine}")
            self.print_substep(f"Python: {python_version}")
            
            # Check if optimal system
            if system == "Darwin" and machine == "x86_64":
                self.print_success("macOS Intel detected (optimal for this project)")
            elif system == "Darwin":
                self.print_warning("macOS ARM detected - should work but designed for Intel")
            else:
                self.print_warning(f"Non-macOS system: {system} - project optimized for macOS")
            
            # Check Python version
            version_parts = [int(x) for x in python_version.split('.')]
            if version_parts[0] >= 3 and version_parts[1] >= 7:
                self.print_success(f"Python {python_version} is compatible")
            else:
                self.print_error(f"Python {python_version} < 3.7 (incompatible)")
                return False
            
            self.passed_checks += 1
            return True
            
        except Exception as e:
            self.print_error(f"System check failed: {e}")
            return False
    
    def check_memory(self) -> bool:
        """Check available system memory."""
        self.print_step("Memory Check")
        self.total_checks += 1
        
        try:
            if platform.system() == "Darwin":
                # macOS memory check
                result = subprocess.run(['sysctl', '-n', 'hw.memsize'], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    memory_bytes = int(result.stdout.strip())
                    memory_gb = memory_bytes / (1024**3)
                    self.print_substep(f"Available memory: {memory_gb:.1f} GB")
                    
                    if memory_gb >= 16:
                        self.print_success("Sufficient memory for experiments")
                    elif memory_gb >= 8:
                        self.print_warning("Limited memory - consider using --quick mode")
                    else:
                        self.print_error("Insufficient memory (< 8GB)")
                        return False
                else:
                    self.print_warning("Could not determine memory amount")
            else:
                self.print_substep("Memory check skipped (non-macOS)")
            
            self.passed_checks += 1
            return True
            
        except Exception as e:
            self.print_warning(f"Memory check failed: {e}")
            self.passed_checks += 1  # Non-critical
            return True
    
    def check_python_packages(self) -> bool:
        """Check required Python packages."""
        self.print_step("Python Package Dependencies")
        self.total_checks += 1
        
        required_packages = {
            'torch': '1.13.0',
            'torchvision': '0.14.0',
            'numpy': '1.21.0',
            'matplotlib': '3.5.0',
            'pandas': '1.3.0',
            'seaborn': '0.11.0'
        }
        
        missing_packages = []
        outdated_packages = []
        
        for package, min_version in required_packages.items():
            try:
                module = importlib.import_module(package)
                version = getattr(module, '__version__', 'unknown')
                
                if version != 'unknown':
                    self.print_substep(f"{package}: {version}")
                    
                    # Simple version comparison (not perfect but sufficient)
                    try:
                        current = [int(x) for x in version.split('.')[:3]]
                        required = [int(x) for x in min_version.split('.')[:3]]
                        
                        # Pad shorter version with zeros
                        while len(current) < 3:
                            current.append(0)
                        while len(required) < 3:
                            required.append(0)
                        
                        if current < required:
                            outdated_packages.append(f"{package} {version} < {min_version}")
                    except:
                        pass  # Skip version comparison if parsing fails
                else:
                    self.print_substep(f"{package}: installed (version unknown)")
                    
            except ImportError:
                missing_packages.append(package)
                self.print_substep(f"{package}: NOT FOUND")
        
        # Check built-in modules
        builtin_modules = ['json', 'csv', 'os', 'sys', 'time']
        for module in builtin_modules:
            try:
                importlib.import_module(module)
                self.print_substep(f"{module}: ✓ (built-in)")
            except ImportError:
                self.print_error(f"{module}: missing (critical built-in module)")
                return False
        
        # Report results
        if missing_packages:
            self.print_error(f"Missing packages: {', '.join(missing_packages)}")
            self.print_substep("Run: pip install -r requirements.txt")
            return False
        
        if outdated_packages:
            self.print_warning(f"Outdated packages: {', '.join(outdated_packages)}")
        
        self.print_success("All required packages are available")
        self.passed_checks += 1
        return True
    
    def check_pytorch_functionality(self) -> bool:
        """Test basic PyTorch functionality."""
        self.print_step("PyTorch Functionality Test")
        self.total_checks += 1
        
        try:
            import torch
            import torch.nn as nn
            import torch.nn.functional as F
            
            # Test basic tensor operations
            x = torch.randn(2, 3)
            y = torch.randn(3, 4)
            z = torch.matmul(x, y)
            self.print_substep("✓ Basic tensor operations")
            
            # Test neural network components
            model = nn.Sequential(
                nn.Linear(10, 5),
                nn.ReLU(),
                nn.Linear(5, 1)
            )
            input_tensor = torch.randn(1, 10)
            output = model(input_tensor)
            self.print_substep("✓ Neural network operations")
            
            # Test gradient computation
            output.backward()
            self.print_substep("✓ Gradient computation")
            
            # Check CUDA availability (informational)
            if torch.cuda.is_available():
                self.print_substep(f"✓ CUDA available ({torch.cuda.device_count()} devices)")
            else:
                self.print_substep("⚠ CUDA not available (CPU only)")
            
            self.print_success("PyTorch functionality verified")
            self.passed_checks += 1
            return True
            
        except Exception as e:
            self.print_error(f"PyTorch test failed: {e}")
            return False
    
    def check_project_structure(self) -> bool:
        """Check project file structure."""
        self.print_step("Project Structure")
        self.total_checks += 1
        
        required_files = [
            'python/data_processing.py',
            'python/adversarial_robustness.py', 
            'python/generate_comparison_plots.py',
            'src/AdversarialRobustness.idr',
            'adversarial_robustness.ipkg',
            'pack.toml',
            'run_experiment.sh',
            'requirements.txt',
            'config.json',
            'README.md'
        ]
        
        required_dirs = ['python', 'src', 'data', 'results', 'plots']
        
        missing_files = []
        missing_dirs = []
        
        # Check files
        for file_path in required_files:
            if os.path.exists(file_path):
                self.print_substep(f"✓ {file_path}")
            else:
                missing_files.append(file_path)
                self.print_substep(f"✗ {file_path}")
        
        # Check/create directories
        for directory in required_dirs:
            if os.path.exists(directory):
                self.print_substep(f"✓ {directory}/")
            else:
                try:
                    os.makedirs(directory, exist_ok=True)
                    self.print_substep(f"✓ {directory}/ (created)")
                except:
                    missing_dirs.append(directory)
                    self.print_substep(f"✗ {directory}/")
        
        if missing_files or missing_dirs:
            if missing_files:
                self.print_error(f"Missing files: {', '.join(missing_files)}")
            if missing_dirs:
                self.print_error(f"Cannot create directories: {', '.join(missing_dirs)}")
            return False
        
        self.print_success("Project structure is complete")
        self.passed_checks += 1
        return True
    
    def check_idris_environment(self) -> bool:
        """Check Idris/Pack environment."""
        self.print_step("Idris Environment (Optional)")
        self.total_checks += 1
        
        try:
            # Check if pack is available
            result = subprocess.run(['pack', '--version'], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                version = result.stdout.strip()
                self.print_substep(f"Pack version: {version}")
                
                # Try to typecheck the project
                typecheck_result = subprocess.run(['pack', 'typecheck', 'adversarial_robustness'],
                                                capture_output=True, text=True)
                
                if typecheck_result.returncode == 0:
                    self.print_success("Idris project typechecks successfully")
                else:
                    self.print_warning("Idris project has type errors (likely missing Spidr)")
                    self.print_substep("This is expected - project will run with demonstrations")
                
            else:
                self.print_warning("Pack not available")
                self.print_substep("Install from: https://github.com/stefan-hoeck/idris2-pack")
                self.print_substep("Idris experiments will be limited without pack")
            
        except FileNotFoundError:
            self.print_warning("Pack not found in PATH")
            self.print_substep("Idris formal verification will use demonstration mode")
        
        # This is non-critical for the main experiment
        self.passed_checks += 1
        return True
    
    def check_configuration(self) -> bool:
        """Check experiment configuration."""
        self.print_step("Configuration Validation")
        self.total_checks += 1
        
        try:
            # Check config.json
            if os.path.exists('config.json'):
                with open('config.json', 'r') as f:
                    config = json.load(f)
                
                required_sections = ['experiment_config', 'dataset', 'neural_network']
                for section in required_sections:
                    if section in config:
                        self.print_substep(f"✓ Config section: {section}")
                    else:
                        self.print_warning(f"Missing config section: {section}")
                
                self.print_success("Configuration file is valid")
            else:
                self.print_warning("config.json not found (will use defaults)")
            
            self.passed_checks += 1
            return True
            
        except Exception as e:
            self.print_warning(f"Configuration check failed: {e}")
            self.passed_checks += 1  # Non-critical
            return True
    
    def run_mini_test(self) -> bool:
        """Run a mini version of the experiment to test functionality."""
        self.print_step("Mini Functionality Test")
        self.total_checks += 1
        
        try:
            # Test that we can import and run basic components
            sys.path.append('python')
            
            # Test data processing components
            import torch
            import torch.nn as nn
            
            # Create a mini neural network
            model = nn.Sequential(
                nn.Linear(784, 64),
                nn.ReLU(),
                nn.Linear(64, 10)
            )
            
            # Test forward pass
            test_input = torch.randn(1, 784)
            output = model(test_input)
            
            if output.shape == (1, 10):
                self.print_substep("✓ Neural network forward pass")
            else:
                self.print_error(f"Wrong output shape: {output.shape}")
                return False
            
            # Test adversarial attack simulation
            epsilon = 0.1
            noise = torch.randn_like(test_input) * epsilon
            perturbed_input = test_input + noise
            perturbed_output = model(perturbed_input)
            
            self.print_substep("✓ Adversarial perturbation simulation")
            
            # Test visualization imports
            import matplotlib.pyplot as plt
            import numpy as np
            
            # Create a simple plot (don't show)
            plt.figure()
            plt.plot([1, 2, 3], [1, 4, 2])
            plt.close()
            
            self.print_substep("✓ Visualization components")
            
            self.print_success("Mini functionality test passed")
            self.passed_checks += 1
            return True
            
        except Exception as e:
            self.print_error(f"Mini test failed: {e}")
            return False
    
    def generate_summary_report(self) -> Dict[str, Any]:
        """Generate a summary report of the validation."""
        
        duration = time.time() - self.start_time
        
        report = {
            'validation_summary': {
                'total_checks': self.total_checks,
                'passed_checks': self.passed_checks,
                'success_rate': (self.passed_checks / self.total_checks * 100) if self.total_checks > 0 else 0,
                'validation_time_seconds': duration,
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
            },
            'system_info': {
                'os': platform.system(),
                'architecture': platform.machine(),
                'python_version': platform.python_version()
            },
            'warnings': self.warnings,
            'errors': self.errors,
            'status': 'PASS' if len(self.errors) == 0 else 'FAIL',
            'recommendations': []
        }
        
        # Add recommendations based on results
        if self.errors:
            report['recommendations'].append("Fix the errors listed above before running experiments")
        
        if self.warnings:
            report['recommendations'].append("Review warnings - they may affect experiment performance")
        
        if len(self.errors) == 0:
            report['recommendations'].extend([
                "Run './run_experiment.sh' for the complete experiment",
                "Use './run_experiment.sh --quick' for faster testing",
                "Check README.md for detailed usage instructions"
            ])
        
        return report
    
    def run_full_validation(self) -> bool:
        """Run all validation checks."""
        
        self.print_header("ADVERSARIAL ROBUSTNESS VERIFICATION - SETUP VALIDATION")
        print("Validating environment for Idris vs Python comparison experiment")
        
        # Run all checks
        checks = [
            self.check_system_info,
            self.check_memory,
            self.check_python_packages,
            self.check_pytorch_functionality,
            self.check_project_structure,
            self.check_idris_environment,
            self.check_configuration,
            self.run_mini_test
        ]
        
        for check in checks:
            try:
                check()
            except Exception as e:
                self.print_error(f"Check failed unexpectedly: {e}")
        
        # Generate and display summary
        report = self.generate_summary_report()
        
        self.print_header("VALIDATION SUMMARY")
        
        success_rate = report['validation_summary']['success_rate']
        print(f"Checks passed: {self.passed_checks}/{self.total_checks} ({success_rate:.1f}%)")
        print(f"Validation time: {report['validation_summary']['validation_time_seconds']:.1f} seconds")
        
        if self.errors:
            print(f"\n{Color.RED}❌ ERRORS ({len(self.errors)}):{Color.END}")
            for error in self.errors:
                print(f"  • {error}")
        
        if self.warnings:
            print(f"\n{Color.YELLOW}⚠ WARNINGS ({len(self.warnings)}):{Color.END}")
            for warning in self.warnings:
                print(f"  • {warning}")
        
        # Final status
        if len(self.errors) == 0:
            print(f"\n{Color.GREEN}🎉 VALIDATION PASSED{Color.END}")
            print("Environment is ready for adversarial robustness verification experiments!")
            print("\nRecommended next steps:")
            for rec in report['recommendations']:
                print(f"  • {rec}")
        else:
            print(f"\n{Color.RED}❌ VALIDATION FAILED{Color.END}")
            print("Please fix the errors above before running experiments.")
        
        # Save detailed report
        try:
            os.makedirs('results', exist_ok=True)
            with open('results/validation_report.json', 'w') as f:
                json.dump(report, f, indent=2)
            print(f"\nDetailed report saved to: results/validation_report.json")
        except:
            pass  # Non-critical
        
        return len(self.errors) == 0

def main():
    """Main validation entry point."""
    
    validator = SetupValidator()
    success = validator.run_full_validation()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()