package adversarial_robustness

version = 0.0.1
authors = "<PERSON><PERSON><PERSON><PERSON>"
brief = "Adversarial robustness verification using dependent types"
readme = "README.md"
homepage = "https://github.com/your-username/adversarial-robustness-verification"
sourceloc = "https://github.com/your-username/adversarial-robustness-verification.git"
bugtracker = "https://github.com/your-username/adversarial-robustness-verification/issues"

depends = base
        , contrib

sourcedir = "src"

modules = AdversarialRobustness

main = AdversarialRobustness

executable = adversarial_robustness

-- Optional: specify additional build options
opts = ""

-- Package-specific options for Spidr integration
-- These ensure proper compilation with tensor operations
builddir = "build"

-- Custom compilation flags for Spidr/tensor operations
-- These may be needed for XLA integration and tensor computations
postinstall = ""

-- Metadata for the dissertation project
-- Project: Accelerated Machine Learning with Dependent Types
-- Subproject: Project 5: Adversarial Robustness Verification
-- Institution: University of St Andrews
-- Supervisor: <PERSON>
-- Student: <PERSON><PERSON><PERSON><PERSON>
-- Student ID: 230029895
-- Year: 2025