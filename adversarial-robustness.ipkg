package adversarial_robustness

version = 0.1.0
authors = "R<PERSON>abh <PERSON> Pat<PERSON>"
brief = "Adversarial robustness verification using dependent types"
readme = "README.md"
homepage = "https://github.com/your-username/adversarial-robustness-verification"
sourceloc = "https://github.com/your-username/adversarial-robustness-verification.git"
bugtracker = "https://github.com/your-username/adversarial-robustness-verification/issues"

depends = base    >= 0.5.1
        , contrib >= 0.5.1
        , network >= 0.5.1
        , json    >= 0.1.0
        , spidr   >= 0.1.0

sourcedir = "src"

modules = AdversarialRobustness

main = AdversarialRobustness

executable = adversarial_robustness

-- Optional: specify additional build options
opts = ""

-- Package-specific options for Spidr integration
-- These ensure proper compilation with tensor operations
builddir = "build"

-- Custom compilation flags for Spidr/tensor operations
-- These may be needed for XLA integration and tensor computations
postinstall = ""

-- <PERSON><PERSON><PERSON> for the dissertation project
metadata = [
    ("project", "Accelerated Machine Learning with Dependent Types"),
    ("subproject", "Project 5: Adversarial Robustness Verification"),
    ("institution", "University of St Andrews"),
    ("supervisor", "<PERSON>"),
    ("student", "Rishabh Ashok Patil"),
    ("student_id", "230029895"),
    ("year", "2025")
]