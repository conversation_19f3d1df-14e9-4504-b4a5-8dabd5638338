# Spidr Library Integration Setup

This document explains how to set up the custom pack edition with <PERSON>pidr library for the adversarial robustness verification project.

## Prerequisites

1. **Custom Pack Installation**: You need the custom edition of pack that includes Spidr support
2. **Idris2**: Version 0.6.0 or later
3. **Spidr Library**: The tensor computation library for Idris

## Installation Steps

### 1. Install Custom Pack with Spidr

```bash
# If you have the custom pack with Spidr support
# Follow the specific installation instructions provided by your supervisor
# This typically involves:

# Clone the custom pack repository
git clone https://github.com/your-custom-pack-repo/pack-spidr.git
cd pack-spidr

# Build and install the custom pack
make bootstrap
make install

# Verify installation
pack --version
```

### 2. Verify Spidr Library Availability

```bash
# Check if Spidr is available in pack
pack search spidr

# Check tensor library
pack search tensor

# List available packages
pack list
```

### 3. Build the Project

```bash
# In the project root directory
pack build adversarial_robustness

# If successful, you should see no errors
# If Spid<PERSON> is missing, you'll get import errors
```

### 4. Alternative Setup (If Custom Pack Not Available)

If the custom pack with <PERSON>pidr is not yet available, you can:

```bash
# Use the demonstration mode
# The Idris code will run with placeholders and show the approach
pack build adversarial_robustness

# Or modify the imports in src/AdversarialRobustness.idr
# Comment out Spidr-specific imports and use placeholders
```

## Spidr Import Structure

The project uses these imports from Spidr:

```idris
-- In src/AdversarialRobustness.idr
import Tensor              -- Main tensor operations
import Tensor.Matrix       -- Matrix operations (if available)
import Tensor.Linear       -- Linear algebra (if available)
```

## Configuration Files

### adversarial_robustness.ipkg
- Defines the Idris package structure
- Specifies Spidr as a dependency
- Configures build options

### pack.toml  
- Configures pack package manager
- Defines Spidr library sources
- Sets up custom compilation options
- Enables XLA integration (if available)

## Troubleshooting

### Error: "Can't find import Tensor"

**Solution 1**: Verify Spidr installation
```bash
pack install spidr
pack install tensor
```

**Solution 2**: Use demonstration mode
```idris
-- In src/AdversarialRobustness.idr
-- Comment out Spidr imports and enable demo mode
-- module AdversarialRobustness

-- import Data.Vect  -- Use basic Idris types instead
-- Tensor will be emulated with placeholder functions
```

### Error: "Can't resolve dependencies"

**Solution**: Update pack configuration
```bash
# Update package database
pack update

# Clean and rebuild
pack clean adversarial_robustness
pack build adversarial_robustness
```

### Error: "XLA backend not found"

**Solution**: Disable XLA in pack.toml
```toml
[build.spidr]
xla_enabled = false
# Use CPU-only computation
```

## Expected Behavior

### With Spidr Available:
- Full tensor operations
- XLA acceleration (if configured)
- Complete formal verification
- Performance optimizations

### Without Spidr (Demo Mode):
- Placeholder tensor operations
- Demonstration of type-level guarantees
- Proof structure without execution
- Comparison framework still functional

## Integration with Python Pipeline

The Idris component integrates with the Python pipeline through:

1. **Data Exchange**: JSON files in `data/` directory
2. **Results Output**: JSON results in `results/` directory  
3. **Comparison**: Automated comparison in `run_experiment.sh`

## Performance Considerations

### With Spidr + XLA:
- Near-native performance for tensor operations
- JIT compilation optimization
- Memory-efficient computation

### Demo Mode:
- Focus on type-level verification
- Compilation time optimization
- Proof checking emphasis

## Research Notes

For the dissertation research:

1. **Document Setup**: Record which version of pack/Spidr was used
2. **Performance Metrics**: Note compilation times and execution performance
3. **Limitations**: Document any constraints in the formal verification
4. **Comparison**: Ensure fair comparison with Python implementation

## Support

If you encounter issues with the Spidr setup:

1. Check with your supervisor for the latest custom pack version
2. Refer to Spidr documentation (if available)
3. Use demonstration mode as fallback
4. Focus on the type-level guarantees and proof structure

The project is designed to work in both full Spidr mode and demonstration mode, ensuring your research can proceed regardless of library availability.