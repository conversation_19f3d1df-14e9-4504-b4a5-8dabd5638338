{"experiment_config": {"name": "adversarial_robustness_verification", "version": "1.0.0", "description": "Comparing Idris formal verification vs Python empirical testing for adversarial robustness"}, "dataset": {"name": "MNIST", "training_subset_size": 10000, "test_subset_size": 1000, "batch_size": 64, "normalization": {"mean": 0.1307, "std": 0.3081}}, "neural_network": {"architecture": [784, 256, 128, 10], "activation": "relu", "training": {"epochs": 5, "learning_rate": 0.001, "optimizer": "adam", "loss_function": "cross_entropy"}}, "adversarial_testing": {"python": {"attack_methods": ["fgsm", "pgd", "random"], "epsilon_values": [0.05, 0.1, 0.15, 0.2, 0.25, 0.3], "pgd_parameters": {"num_iterations": 10, "step_size": 0.01}, "test_samples": 100}, "idris": {"verification_method": "formal_proof", "epsilon_target": 0.1, "model_simplification": "linear_approximation", "proof_timeout_seconds": 300}}, "evaluation_metrics": {"correctness": ["runtime_errors_vs_compile_errors", "attack_success_rate", "robustness_rate", "verification_coverage"], "performance": ["execution_time", "compilation_time", "memory_usage", "scalability_limit"], "development": ["lines_of_code", "implementation_time", "learning_curve", "debugging_effort"], "usability": ["ecosystem_support", "tooling_quality", "error_message_clarity", "integration_ease"]}, "output_settings": {"save_detailed_results": true, "generate_plots": true, "create_comparison_report": true, "verbose_logging": true, "save_model_weights": true, "export_formats": ["json", "csv", "png"]}, "system_requirements": {"python_version": ">=3.7", "pytorch_version": ">=1.13.0", "idris_version": ">=0.5.1", "memory_gb": 16, "cpu_cores": 4}, "reproducibility": {"random_seed": 42, "deterministic_algorithms": true, "hardware_info_logging": true, "version_tracking": true}, "research_focus": {"primary_objectives": ["error_prevention_comparison", "verification_strength_analysis", "performance_trade_offs"], "secondary_objectives": ["developer_productivity_assessment", "scalability_evaluation", "ecosystem_maturity_comparison"], "key_research_questions": ["Can dependent types provide stronger robustness guarantees than empirical testing?", "What is the trade-off between verification strength and model complexity?", "How does formal verification affect development productivity in ML?", "What are the practical limitations of each approach?"]}}