1/1: Building AdversarialRobustness (src/AdversarialRobustness.idr)
Warning: We are about to implicitly bind the following lowercase names.
You may be unintentionally shadowing the associated global definitions:
  input is shadowing System.File.Process.SubProcess.input
  output is shadowing System.File.Process.SubProcess.output

AdversarialRobustness:35:3--35:62
 31 | 
 32 | -- | Simple feedforward network architecture  
 33 | public export
 34 | data Network : List Nat -> Type where
 35 |   SingleLayer : Layer input output -> Network [input, output]
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

Warning: We are about to implicitly bind the following lowercase names.
You may be unintentionally shadowing the associated global definitions:
  input is shadowing System.File.Process.SubProcess.input

AdversarialRobustness:36:3--36:100
 32 | -- | Simple feedforward network architecture  
 33 | public export
 34 | data Network : List Nat -> Type where
 35 |   SingleLayer : Layer input output -> Network [input, output]
 36 |   AddLayer    : Layer input hidden -> Network (hidden :: rest) -> Network (input :: hidden :: rest)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

Warning: We are about to implicitly bind the following lowercase names.
You may be unintentionally shadowing the associated global definitions:
  output is shadowing System.File.Process.SubProcess.output
  input is shadowing System.File.Process.SubProcess.input

AdversarialRobustness:112:1--115:34
 112 | linearVector : Vect output (Vect input Double) ->
 113 |                Vect output Double ->
 114 |                Vect input Double ->
 115 |                Vect output Double

Warning: We are about to implicitly bind the following lowercase names.
You may be unintentionally shadowing the associated global definitions:
  input is shadowing System.File.Process.SubProcess.input
  output is shadowing System.File.Process.SubProcess.output

AdversarialRobustness:122:1--122:66
 118 |   biases
 119 | 
 120 | -- | Forward pass through a single layer
 121 | public export
 122 | forwardLayer : Layer input output -> Input input -> Output output
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

Warning: We are about to implicitly bind the following lowercase names.
You may be unintentionally shadowing the associated global definitions:
  input is shadowing System.File.Process.SubProcess.input
  output is shadowing System.File.Process.SubProcess.output

AdversarialRobustness:128:1--128:73
 124 |   reluVector (linearVector weights biases input)
 125 | 
 126 | -- | Forward pass through network (simplified to single layer)
 127 | public export
 128 | forwardNetwork : Network [input, output] -> Input input -> Output output
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

Now compiling the executable: adversarial_robustness
