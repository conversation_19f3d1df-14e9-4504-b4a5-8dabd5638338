{"success": true, "training_metrics": {"training_time": 8.880131006240845, "epoch_times": [1.9254720211029053, 1.7625930309295654, 1.7985260486602783, 1.7308261394500732, 1.6626019477844238], "losses": [0.50154446634897, 0.20928700057089708, 0.1411765334048089, 0.09924155311172556, 0.06687835175662664], "accuracies": [85.17, 93.57, 95.48, 96.97, 97.94], "final_accuracy": 97.94, "epochs": 5, "total_batches": 785}, "test_metrics": {"test_accuracy": 94.2, "correct_predictions": 942, "total_samples": 1000, "evaluation_time": 0.16391706466674805, "class_accuracies": {"class_0": 98.82352941176471, "class_1": 99.2063492063492, "class_2": 94.82758620689656, "class_3": 91.58878504672897, "class_4": 96.36363636363636, "class_5": 87.35632183908046, "class_6": 94.25287356321839, "class_7": 95.95959595959596, "class_8": 94.38202247191012, "class_9": 87.23404255319149}}, "metadata": {"experiment_info": {"dataset": "MNIST", "architecture": "[784, 256, 128, 10]", "training_samples": 10000, "test_samples": 1000, "framework": "PyTorch", "timestamp": "2025-07-07 22:32:32"}, "training_results": {"epochs": 5, "final_training_accuracy": 97.94, "training_time_seconds": 8.880131006240845, "average_epoch_time": 1.7760038375854492, "final_loss": 0.06687835175662664}, "test_results": {"test_accuracy": 94.2, "evaluation_time_seconds": 0.16391706466674805, "correct_predictions": 942, "total_test_samples": 1000, "class_accuracies": {"class_0": 98.82352941176471, "class_1": 99.2063492063492, "class_2": 94.82758620689656, "class_3": 91.58878504672897, "class_4": 96.36363636363636, "class_5": 87.35632183908046, "class_6": 94.25287356321839, "class_7": 95.95959595959596, "class_8": 94.38202247191012, "class_9": 87.23404255319149}}, "model_info": {"total_parameters": null, "model_size_mb": null, "architecture_depth": 3, "activation_function": "relu"}}}