#!/bin/bash

# =============================================================================
# Setup Script for Adversarial Robustness Verification Project
# =============================================================================
# 
# This script sets up the environment for running the adversarial robustness
# verification experiments comparing Idris formal verification vs Python
# empirical testing.
#
# Usage: ./setup.sh [options]
# Options:
#   --python-only    Setup only Python environment
#   --check-only     Only check current setup status
#   --help           Show this help message

set -e  # Exit on any error

# =============================================================================
# CONFIGURATION
# =============================================================================

PYTHON_ENV="python3"
PACK_CMD="pack"
PROJECT_ROOT="$(pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# =============================================================================
# HELPER FUNCTIONS
# =============================================================================

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

print_step() {
    echo -e "${GREEN}[SETUP]${NC} $1"
}

print_substep() {
    echo -e "${PURPLE}  →${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# =============================================================================
# SYSTEM CHECKS
# =============================================================================

check_system_requirements() {
    print_step "Checking system requirements..."
    
    # Check operating system
    OS=$(uname -s)
    ARCH=$(uname -m)
    
    print_substep "Operating System: $OS"
    print_substep "Architecture: $ARCH"
    
    if [[ "$OS" == "Darwin" && "$ARCH" == "x86_64" ]]; then
        print_substep "✓ macOS Intel detected (optimal for this project)"
    elif [[ "$OS" == "Darwin" ]]; then
        print_warning "macOS ARM detected - project designed for Intel but should work"
    else
        print_warning "Non-macOS system detected - project designed for macOS Intel"
    fi
    
    # Check available memory
    if [[ "$OS" == "Darwin" ]]; then
        MEMORY_BYTES=$(sysctl -n hw.memsize)
        MEMORY_GB=$((MEMORY_BYTES / 1024 / 1024 / 1024))
        print_substep "Available Memory: ${MEMORY_GB} GB"
        
        if [[ $MEMORY_GB -ge 16 ]]; then
            print_substep "✓ Sufficient memory for experiments"
        else
            print_warning "Less than 16GB memory - consider using --quick mode"
        fi
    fi
    
    print_success "System requirements check complete"
}

check_python_environment() {
    print_step "Checking Python environment..."
    
    # Check Python version
    if command -v $PYTHON_ENV &> /dev/null; then
        PYTHON_VERSION=$($PYTHON_ENV --version 2>&1 | cut -d' ' -f2)
        print_substep "Python version: $PYTHON_VERSION"
        
        # Check if version is >= 3.7
        PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d. -f1)
        PYTHON_MINOR=$(echo $PYTHON_VERSION | cut -d. -f2)
        
        if [[ $PYTHON_MAJOR -ge 3 && $PYTHON_MINOR -ge 7 ]]; then
            print_substep "✓ Python version is compatible"
        else
            print_error "Python version must be >= 3.7"
            return 1
        fi
    else
        print_error "Python3 not found. Please install Python 3.7+"
        return 1
    fi
    
    # Check pip
    if $PYTHON_ENV -m pip --version &> /dev/null; then
        print_substep "✓ pip is available"
    else
        print_error "pip not available. Please install pip"
        return 1
    fi
    
    print_success "Python environment check complete"
}

check_idris_environment() {
    print_step "Checking Idris environment..."
    
    if command -v $PACK_CMD &> /dev/null; then
        PACK_VERSION=$($PACK_CMD --version 2>&1 || echo "unknown")
        print_substep "Pack version: $PACK_VERSION"
        print_substep "✓ Pack is available"
    else
        print_warning "Pack not found - Idris experiments will be limited"
        print_substep "Install from: https://github.com/stefan-hoeck/idris2-pack"
        return 1
    fi
    
    print_success "Idris environment check complete"
}

# =============================================================================
# SETUP FUNCTIONS
# =============================================================================

create_project_structure() {
    print_step "Creating project directory structure..."
    
    # Create main directories
    mkdir -p data results plots python src
    
    # Check if required directories exist
    if [[ -d "python" && -d "src" && -d "data" && -d "results" && -d "plots" ]]; then
        print_substep "✓ Project directories created"
    else
        print_error "Failed to create project directories"
        return 1
    fi
    
    print_success "Project structure setup complete"
}

install_python_dependencies() {
    print_step "Installing Python dependencies..."
    
    if [[ -f "requirements.txt" ]]; then
        print_substep "Installing from requirements.txt..."
        
        # Try to install with pip
        if $PYTHON_ENV -m pip install -r requirements.txt; then
            print_substep "✓ Requirements installed successfully"
        else
            print_warning "Some packages may have failed to install"
        fi
    else
        print_warning "requirements.txt not found, installing core packages..."
        
        # Install core packages manually
        CORE_PACKAGES="torch torchvision numpy matplotlib seaborn pandas"
        for package in $CORE_PACKAGES; do
            print_substep "Installing $package..."
            $PYTHON_ENV -m pip install $package
        done
    fi
    
    # Verify key packages
    print_substep "Verifying package installations..."
    
    if $PYTHON_ENV -c "import torch, torchvision, numpy, matplotlib" 2>/dev/null; then
        print_substep "✓ Core packages verified"
    else
        print_error "Failed to verify core packages"
        return 1
    fi
    
    print_success "Python dependencies setup complete"
}

setup_idris_project() {
    print_step "Setting up Idris project..."
    
    if ! command -v $PACK_CMD &> /dev/null; then
        print_warning "Pack not available, skipping Idris setup"
        return 0
    fi
    
    # Check if project can be built (basic typecheck)
    print_substep "Checking Idris project configuration..."
    
    if [[ -f "adversarial_robustness.ipkg" && -f "pack.toml" ]]; then
        print_substep "✓ Idris project files found"
    else
        print_error "Missing Idris project files"
        return 1
    fi
    
    # Try a basic typecheck (will fail if Spidr not available, but that's expected)
    if $PACK_CMD typecheck adversarial_robustness &> /dev/null; then
        print_substep "✓ Idris project typechecks successfully"
    else
        print_warning "Idris project has issues (likely missing Spidr library)"
        print_substep "This is expected - project will run with demonstrations"
    fi
    
    print_success "Idris project setup complete"
}

# =============================================================================
# VALIDATION FUNCTIONS
# =============================================================================

run_validation_tests() {
    print_step "Running validation tests..."
    
    # Test Python environment
    print_substep "Testing Python implementation..."
    
    # Create a simple test
    TEST_PYTHON=$(cat << 'EOF'
import torch
import numpy as np
print("✓ PyTorch:", torch.__version__)
print("✓ NumPy:", np.__version__)

# Test basic tensor operations
x = torch.randn(2, 3)
y = torch.randn(3, 2)
z = torch.mm(x, y)
print("✓ Basic tensor operations working")

# Test if CUDA is available (optional)
if torch.cuda.is_available():
    print("✓ CUDA available")
else:
    print("⚠ CUDA not available (CPU only)")
EOF
)
    
    if echo "$TEST_PYTHON" | $PYTHON_ENV; then
        print_substep "✓ Python validation passed"
    else
        print_error "Python validation failed"
        return 1
    fi
    
    # Test file structure
    print_substep "Testing project structure..."
    
    REQUIRED_FILES=(
        "python/data_processing.py"
        "python/adversarial_robustness.py"
        "python/generate_comparison_plots.py"
        "src/AdversarialRobustness.idr"
        "adversarial_robustness.ipkg"
        "pack.toml"
        "run_experiment.sh"
    )
    
    missing_files=()
    for file in "${REQUIRED_FILES[@]}"; do
        if [[ ! -f "$file" ]]; then
            missing_files+=("$file")
        fi
    done
    
    if [[ ${#missing_files[@]} -eq 0 ]]; then
        print_substep "✓ All required files present"
    else
        print_error "Missing files: ${missing_files[*]}"
        return 1
    fi
    
    print_success "Validation tests complete"
}

# =============================================================================
# MAIN SETUP FUNCTION
# =============================================================================

run_setup() {
    local python_only=0
    local check_only=0
    
    # Parse arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --python-only)
                python_only=1
                shift
                ;;
            --check-only)
                check_only=1
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    print_header "ADVERSARIAL ROBUSTNESS VERIFICATION - SETUP"
    echo "Setting up environment for Idris vs Python comparison experiment"
    echo ""
    
    # System checks
    check_system_requirements
    echo ""
    
    # Python environment
    check_python_environment
    echo ""
    
    # Idris environment (unless python-only)
    if [[ $python_only -eq 0 ]]; then
        check_idris_environment
        echo ""
    fi
    
    # If check-only, stop here
    if [[ $check_only -eq 1 ]]; then
        print_info "Check-only mode complete"
        exit 0
    fi
    
    # Setup steps
    create_project_structure
    echo ""
    
    install_python_dependencies
    echo ""
    
    if [[ $python_only -eq 0 ]]; then
        setup_idris_project
        echo ""
    fi
    
    # Validation
    run_validation_tests
    echo ""
    
    # Final status
    print_header "SETUP COMPLETE"
    echo "🎉 Environment setup successful!"
    echo ""
    echo "Next steps:"
    echo "1. Run the complete experiment:"
    echo "   ./run_experiment.sh"
    echo ""
    echo "2. Or run individual components:"
    echo "   ./run_experiment.sh --python-only"
    echo "   ./run_experiment.sh --idris-only"
    echo ""
    echo "3. For quick testing:"
    echo "   ./run_experiment.sh --quick"
    echo ""
    echo "📁 Project structure ready:"
    echo "   python/    - Python implementation"
    echo "   src/       - Idris source code"
    echo "   data/      - Generated datasets (after running)"
    echo "   results/   - Experimental results (after running)"
    echo "   plots/     - Visualizations (after running)"
    echo ""
}

show_help() {
    echo "Setup Script for Adversarial Robustness Verification"
    echo ""
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  --python-only    Setup only Python environment"
    echo "  --check-only     Only check current setup status"
    echo "  --help           Show this help message"
    echo ""
    echo "This script prepares the environment for comparing formal verification"
    echo "(Idris) vs empirical testing (Python) for adversarial robustness."
    echo ""
    echo "Requirements:"
    echo "  - macOS Intel (recommended) or compatible system"
    echo "  - Python 3.7+"
    echo "  - 16GB RAM (recommended)"
    echo "  - Pack (for Idris experiments)"
}

# =============================================================================
# EXECUTION
# =============================================================================

# Make script executable if run directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    run_setup "$@"
fi