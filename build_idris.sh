#!/bin/bash

# =============================================================================
# Idris Build Script with Spidr Integration
# =============================================================================
# 
# This script builds and runs the Idris adversarial robustness verification
# component with intelligent Spidr detection and integration.
#
# Usage: ./build_idris.sh [--verbose] [--clean] [--spidr-only]

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PACK_CMD="pack"
IDRIS_CMD="idris2"
PROJECT_FILE="adversarial-robustness.ipkg"
EXECUTABLE_PATH="build/exec/adversarial_robustness"
RESULTS_DIR="results"
SPIDR_AVAILABLE=0
VERBOSE=0
CLEAN_BUILD=0
SPIDR_ONLY=0

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_verbose() {
    if [[ $VERBOSE -eq 1 ]]; then
        echo -e "${BLUE}[VERBOSE]${NC} $1"
    fi
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --verbose)
                VERBOSE=1
                shift
                ;;
            --clean)
                CLEAN_BUILD=1
                shift
                ;;
            --spidr-only)
                SPIDR_ONLY=1
                shift
                ;;
            --help)
                echo "Usage: $0 [--verbose] [--clean] [--spidr-only]"
                echo "  --verbose    Enable verbose output"
                echo "  --clean      Clean build before compiling"
                echo "  --spidr-only Use only Spidr (skip pack/idris2 fallback)"
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
}

# Check for Spidr availability
check_spidr() {
    log_info "Checking for Spidr availability..."
    
    if command -v spidr &> /dev/null; then
        SPIDR_AVAILABLE=1
        local spidr_version=$(spidr --version 2>&1 || echo "unknown")
        log_success "Spidr found: $spidr_version"
        log_verbose "Spidr will be used for intelligent build management"
        return 0
    else
        log_warning "Spidr not found"
        if [[ $SPIDR_ONLY -eq 1 ]]; then
            log_error "Spidr-only mode requested but Spidr not available"
            exit 1
        fi
        return 1
    fi
}

# Check for Pack availability
check_pack() {
    if [[ $SPIDR_ONLY -eq 1 ]]; then
        log_verbose "Skipping pack check (Spidr-only mode)"
        return 1
    fi
    
    log_info "Checking for Pack availability..."
    
    if command -v $PACK_CMD &> /dev/null; then
        local pack_version=$($PACK_CMD --version 2>&1 || echo "unknown")
        log_success "Pack found: $pack_version"
        return 0
    else
        log_warning "Pack not found"
        return 1
    fi
}

# Check for Idris2 availability
check_idris2() {
    if [[ $SPIDR_ONLY -eq 1 ]]; then
        log_verbose "Skipping idris2 check (Spidr-only mode)"
        return 1
    fi
    
    log_info "Checking for Idris2 availability..."
    
    if command -v $IDRIS_CMD &> /dev/null; then
        local idris_version=$($IDRIS_CMD --version 2>&1 || echo "unknown")
        log_success "Idris2 found: $idris_version"
        return 0
    else
        log_warning "Idris2 not found"
        return 1
    fi
}

# Clean build directory
clean_build() {
    if [[ $CLEAN_BUILD -eq 1 ]]; then
        log_info "Cleaning build directory..."
        rm -rf build/
        log_success "Build directory cleaned"
    fi
}

# Build with Spidr
build_with_spidr() {
    log_info "Building with Spidr..."
    
    # Create results directory if it doesn't exist
    mkdir -p "$RESULTS_DIR"
    
    # Use Spidr to build the project
    if [[ $VERBOSE -eq 1 ]]; then
        spidr build --verbose 2>&1 | tee "$RESULTS_DIR/idris_build_output.txt"
    else
        spidr build 2>&1 | tee "$RESULTS_DIR/idris_build_output.txt"
    fi
    
    log_success "Spidr build completed"
}

# Build with Pack
build_with_pack() {
    log_info "Building with Pack..."
    log_warning "Pack appears to hang on this system, falling back to idris2 directly"

    # Pack seems to hang, so return failure to trigger idris2 fallback
    return 1
}

# Build with Idris2 directly
build_with_idris2() {
    log_info "Building with Idris2 directly..."
    
    # Create results directory if it doesn't exist
    mkdir -p "$RESULTS_DIR"
    
    # Use Idris2 to build the project
    if [[ $VERBOSE -eq 1 ]]; then
        $IDRIS_CMD --build $PROJECT_FILE --verbose 2>&1 | tee "$RESULTS_DIR/idris_build_output.txt"
    else
        $IDRIS_CMD --build $PROJECT_FILE 2>&1 | tee "$RESULTS_DIR/idris_build_output.txt"
    fi
    
    log_success "Idris2 build completed"
}

# Run the executable and capture output
run_executable() {
    log_info "Running Idris adversarial robustness verification..."
    
    if [[ ! -f "$EXECUTABLE_PATH" ]]; then
        log_error "Executable not found at $EXECUTABLE_PATH"
        return 1
    fi
    
    # Run the executable and capture output
    local output_file="$RESULTS_DIR/idris_output.txt"
    local timing_file="$RESULTS_DIR/idris_timing.json"
    
    local start_time=$(date +%s.%N)
    
    if ./"$EXECUTABLE_PATH" 2>&1 | tee "$output_file"; then
        local end_time=$(date +%s.%N)
        local duration=$(echo "$end_time - $start_time" | bc 2>/dev/null || echo "0")
        
        # Create timing information
        cat > "$timing_file" << EOF
{
  "idris_status": "success",
  "execution_time": $duration,
  "timestamp": "$(date +%Y-%m-%d\ %H:%M:%S)",
  "executable_path": "$EXECUTABLE_PATH",
  "output_file": "$output_file"
}
EOF
        
        log_success "Idris execution completed in ${duration}s"
        log_success "Output saved to $output_file"
        log_success "Timing saved to $timing_file"
        return 0
    else
        local end_time=$(date +%s.%N)
        local duration=$(echo "$end_time - $start_time" | bc 2>/dev/null || echo "0")
        
        # Create error timing information
        cat > "$timing_file" << EOF
{
  "idris_status": "execution_failed",
  "execution_time": $duration,
  "timestamp": "$(date +%Y-%m-%d\ %H:%M:%S)",
  "executable_path": "$EXECUTABLE_PATH",
  "output_file": "$output_file",
  "error": "execution_failed"
}
EOF
        
        log_error "Idris execution failed"
        return 1
    fi
}

# Main build function
main() {
    parse_args "$@"
    
    log_info "Starting Idris build with Spidr integration..."
    log_verbose "Project file: $PROJECT_FILE"
    log_verbose "Executable path: $EXECUTABLE_PATH"
    log_verbose "Results directory: $RESULTS_DIR"
    
    # Clean build if requested
    clean_build
    
    # Check available tools and build
    local build_success=0
    
    if check_spidr && [[ $SPIDR_AVAILABLE -eq 1 ]]; then
        log_info "Using Spidr for intelligent build management"
        if build_with_spidr; then
            build_success=1
        fi
    elif check_pack; then
        log_info "Using Pack for build management"
        if build_with_pack; then
            build_success=1
        else
            log_warning "Pack build failed, trying Idris2 directly as fallback"
            if check_idris2 && build_with_idris2; then
                build_success=1
            fi
        fi
    elif check_idris2; then
        log_info "Using Idris2 directly"
        if build_with_idris2; then
            build_success=1
        fi
    else
        log_error "No suitable build tool found (Spidr, Pack, or Idris2)"
        exit 1
    fi
    
    if [[ $build_success -eq 1 ]]; then
        log_success "Build completed successfully"
        
        # Run the executable
        if run_executable; then
            log_success "Idris adversarial robustness verification completed"
            exit 0
        else
            log_error "Execution failed"
            exit 1
        fi
    else
        log_error "Build failed"
        exit 1
    fi
}

# Run main function with all arguments
main "$@"
