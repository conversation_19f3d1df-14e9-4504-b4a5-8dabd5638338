{"experiment_name": "Adversarial Robustness Verification Comparison", "version": "2.0_controlled", "description": "Controlled experiment comparing formal verification (Idris) vs empirical testing (Python) with identical test conditions", "scientific_controls": {"sample_size": 20, "rationale": "Sufficient for statistical significance while remaining tractable for formal verification", "identical_test_data": true, "controlled_conditions": true}, "test_configuration": {"num_test_samples": 20, "sample_selection": "first_20_from_dataset", "epsilon_values": [0.05, 0.1, 0.15], "random_seed": 42}, "python_config": {"model_architecture": [784, 256, 128, 10], "model_type": "complex_neural_network", "attacks": ["fgsm", "pgd"], "batch_size": 4, "max_samples": 20}, "idris_config": {"model_type": "simplified_linear_model", "verification_approach": "formal_mathematical_proofs", "max_samples": 20, "epsilon_bound": 0.1, "margin_assumption": 2.0}, "comparison_fairness": {"acknowledge_model_complexity_difference": true, "focus_on_approach_comparison": true, "identical_test_samples": true, "identical_sample_count": true, "clear_limitation_disclosure": true}, "output_requirements": {"report_sample_sizes": true, "report_model_differences": true, "report_guarantee_types": true, "avoid_biased_conclusions": true}}