# Python Dependencies for Adversarial Robustness Verification Project
# =================================================================
# Project: Accelerated Machine Learning with Dependent Types - Project 5
# Comparing Idris formal verification vs Python empirical testing

# Core ML and Scientific Computing
torch>=1.13.0
torchvision>=0.14.0
numpy>=1.21.0
scipy>=1.7.0

# Data Manipulation and Analysis
pandas>=1.3.0

# Visualization and Plotting
matplotlib>=3.5.0
seaborn>=0.11.0

# Progress bars and utilities
tqdm>=4.62.0

# Development and Testing (optional but recommended)
pytest>=6.0.0
black>=22.0.0
flake8>=4.0.0

# JSON handling (built-in but listed for completeness)
# json - built-in Python module

# File handling and CSV processing
# csv - built-in Python module

# Additional utilities
typing-extensions>=4.0.0

# Note: Idris dependencies are managed through pack
# Install pack from: https://github.com/stefan-hoeck/idris2-pack
# Install Spidr tensor library for complete Idris functionality (experimental)

# Installation instructions:
# ========================
# 1. pip install -r requirements.txt
# 2. For macOS with Apple Silicon: pip install torch torchvision --index-url https://download.pytorch.org/whl/cpu
# 3. For CUDA support (optional): visit https://pytorch.org for GPU-specific instructions

# Tested versions:
# ================
# Python: 3.7+
# PyTorch: 1.13.0+
# NumPy: 1.21.0+
# System: macOS Intel (recommended), Linux, Windows with appropriate Python setup