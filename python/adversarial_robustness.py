#!/usr/bin/env python3
"""
Adversarial Robustness Testing - Python Implementation
====================================================

Empirical testing of neural network robustness using gradient-based adversarial attacks.
Tests robustness against small input perturbations and measures attack success rates.

Approach: Heuristic testing with no formal guarantees
"""

import os
import json
import time
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader, TensorDataset
import matplotlib.pyplot as plt
from typing import Tuple, List, Dict, Any
import csv

# Simple implementation of adversarial attacks (avoiding external dependencies)
class AdversarialAttacks:
    """Collection of simple adversarial attack methods."""
    
    @staticmethod
    def fgsm_attack(model, data, target, epsilon):
        """Fast Gradient Sign Method attack."""
        data.requires_grad = True
        
        output = model(data)
        loss = F.cross_entropy(output, target)
        model.zero_grad()
        loss.backward()
        
        # Create adversarial example
        data_grad = data.grad.data
        perturbed_data = data + epsilon * data_grad.sign()
        perturbed_data = torch.clamp(perturbed_data, 0, 1)  # Keep in valid range
        
        return perturbed_data
    
    @staticmethod
    def pgd_attack(model, data, target, epsilon, alpha=0.01, num_iter=10):
        """Projected Gradient Descent attack."""
        perturbed_data = data.clone().detach()
        
        for _ in range(num_iter):
            perturbed_data.requires_grad = True
            output = model(perturbed_data)
            loss = F.cross_entropy(output, target)
            
            model.zero_grad()
            loss.backward()
            
            # Update
            perturbed_data = perturbed_data + alpha * perturbed_data.grad.sign()
            # Project back to epsilon ball
            perturbation = torch.clamp(perturbed_data - data, -epsilon, epsilon)
            perturbed_data = torch.clamp(data + perturbation, 0, 1).detach()
        
        return perturbed_data
    
    @staticmethod
    def random_noise_attack(data, epsilon):
        """Simple random noise attack."""
        noise = torch.randn_like(data) * epsilon
        perturbed_data = data + noise
        return torch.clamp(perturbed_data, 0, 1)

class SimpleNetwork(nn.Module):
    """Simple feedforward neural network (same as data_processing.py)."""
    
    def __init__(self):
        super(SimpleNetwork, self).__init__()
        self.fc1 = nn.Linear(784, 256)
        self.fc2 = nn.Linear(256, 128)
        self.fc3 = nn.Linear(128, 10)
        
    def forward(self, x):
        x = x.view(-1, 784)
        x = F.relu(self.fc1(x))
        x = F.relu(self.fc2(x))
        x = self.fc3(x)
        return x

class RobustnessEvaluator:
    """Evaluates adversarial robustness of neural networks."""
    
    def __init__(self, model: nn.Module):
        self.model = model
        self.model.eval()
        
    def test_robustness(self, data_loader: DataLoader, epsilon: float, 
                       attack_methods: List[str] = None) -> Dict[str, Any]:
        """Test robustness against various attacks."""
        
        if attack_methods is None:
            attack_methods = ['fgsm', 'pgd', 'random']
        
        results = {}
        
        for attack_name in attack_methods:
            print(f"\nTesting {attack_name.upper()} attack with epsilon={epsilon}")
            
            clean_correct = 0
            adv_correct = 0
            total_samples = 0
            attack_success_count = 0
            
            perturbation_norms = []
            confidence_drops = []
            misclassification_details = []
            
            start_time = time.time()
            
            for batch_idx, (data, target) in enumerate(data_loader):
                # Get clean predictions
                with torch.no_grad():
                    clean_output = self.model(data)
                    clean_pred = clean_output.argmax(dim=1)
                    clean_confidence = F.softmax(clean_output, dim=1).max(dim=1)[0]
                
                # Generate adversarial examples
                if attack_name == 'fgsm':
                    adv_data = AdversarialAttacks.fgsm_attack(self.model, data, target, epsilon)
                elif attack_name == 'pgd':
                    adv_data = AdversarialAttacks.pgd_attack(self.model, data, target, epsilon)
                elif attack_name == 'random':
                    adv_data = AdversarialAttacks.random_noise_attack(data, epsilon)
                
                # Get adversarial predictions
                with torch.no_grad():
                    adv_output = self.model(adv_data)
                    adv_pred = adv_output.argmax(dim=1)
                    adv_confidence = F.softmax(adv_output, dim=1).max(dim=1)[0]
                
                # Calculate metrics
                clean_correct += clean_pred.eq(target).sum().item()
                adv_correct += adv_pred.eq(target).sum().item()
                total_samples += target.size(0)
                
                # Attack success: clean prediction correct but adversarial wrong
                clean_right = clean_pred.eq(target)
                adv_wrong = ~adv_pred.eq(target)
                successful_attacks = (clean_right & adv_wrong).sum().item()
                attack_success_count += successful_attacks
                
                # Detailed misclassification tracking
                for i in range(data.size(0)):
                    if clean_right[i] and adv_wrong[i]:
                        misclassification_details.append({
                            'true_label': target[i].item(),
                            'clean_prediction': clean_pred[i].item(),
                            'adversarial_prediction': adv_pred[i].item(),
                            'clean_confidence': clean_confidence[i].item(),
                            'adversarial_confidence': adv_confidence[i].item(),
                            'confidence_drop': (clean_confidence[i] - adv_confidence[i]).item()
                        })
                
                # Calculate perturbation norms
                perturbation = (adv_data - data).view(data.size(0), -1)
                batch_norms = torch.norm(perturbation, p=float('inf'), dim=1)
                perturbation_norms.extend(batch_norms.tolist())
                
                # Calculate confidence drops
                conf_drop = (clean_confidence - adv_confidence).abs()
                confidence_drops.extend(conf_drop.tolist())
                
                if batch_idx % 10 == 0:
                    print(f'  Batch {batch_idx}/{len(data_loader)} processed')
            
            test_time = time.time() - start_time
            
            # Calculate final metrics
            clean_accuracy = 100. * clean_correct / total_samples
            adv_accuracy = 100. * adv_correct / total_samples
            attack_success_rate = 100. * attack_success_count / total_samples
            robustness_rate = 100. - attack_success_rate
            
            results[attack_name] = {
                'clean_accuracy': clean_accuracy,
                'adversarial_accuracy': adv_accuracy,
                'attack_success_rate': attack_success_rate,
                'robustness_rate': robustness_rate,
                'attack_success_count': attack_success_count,
                'total_samples': total_samples,
                'avg_perturbation_norm': np.mean(perturbation_norms),
                'max_perturbation_norm': np.max(perturbation_norms),
                'min_perturbation_norm': np.min(perturbation_norms),
                'avg_confidence_drop': np.mean(confidence_drops),
                'max_confidence_drop': np.max(confidence_drops),
                'test_time_seconds': test_time,
                'misclassification_details': misclassification_details[:10],  # Save first 10 examples
                'perturbation_norm_distribution': {
                    'mean': np.mean(perturbation_norms),
                    'std': np.std(perturbation_norms),
                    'min': np.min(perturbation_norms),
                    'max': np.max(perturbation_norms),
                    'percentiles': {
                        '25': np.percentile(perturbation_norms, 25),
                        '50': np.percentile(perturbation_norms, 50),
                        '75': np.percentile(perturbation_norms, 75),
                        '90': np.percentile(perturbation_norms, 90)
                    }
                }
            }
            
            print(f"  Clean accuracy: {clean_accuracy:.2f}%")
            print(f"  Adversarial accuracy: {adv_accuracy:.2f}%")
            print(f"  Attack success rate: {attack_success_rate:.2f}%")
            print(f"  Robustness rate: {robustness_rate:.2f}%")
            print(f"  Test time: {test_time:.2f} seconds")
        
        return results
    
    def test_epsilon_range(self, data_loader: DataLoader, 
                          epsilon_values: List[float]) -> Dict[str, Any]:
        """Test robustness across different epsilon values."""
        
        epsilon_results = {}
        
        for eps in epsilon_values:
            print(f"\n{'='*60}")
            print(f"Testing with epsilon = {eps}")
            print('='*60)
            
            results = self.test_robustness(data_loader, eps, ['fgsm', 'pgd'])
            epsilon_results[str(eps)] = results
        
        return epsilon_results

def load_test_data() -> DataLoader:
    """Load test data from saved files with controlled sample size for fair comparison."""

    # Load experiment configuration for scientific integrity
    import json
    try:
        with open('../config/experiment_config.json', 'r') as f:
            config = json.load(f)
        max_samples = config['test_configuration']['num_test_samples']
        print(f"Loading controlled test dataset: {max_samples} samples for fair comparison with Idris")
    except FileNotFoundError:
        max_samples = 20  # Default controlled sample size
        print(f"Config not found, using default controlled sample size: {max_samples}")

    with open('../data/test_samples.json', 'r') as f:
        test_data = json.load(f)

    # SCIENTIFIC CONTROL: Use only the first N samples to match Idris exactly
    test_data = test_data[:max_samples]
    print(f"✓ Using first {len(test_data)} samples for controlled comparison")
    print("Note: This ensures identical test conditions between Python and Idris approaches")

    # Convert to tensors
    images = []
    labels = []

    for sample in test_data:
        # Reshape flattened image back to 28x28
        image = torch.tensor(sample['flattened']).view(1, 28, 28).float()
        images.append(image)
        labels.append(sample['label'])

    images = torch.stack(images)
    labels = torch.tensor(labels)

    dataset = TensorDataset(images, labels)
    # Use smaller batch size for controlled experiment
    data_loader = DataLoader(dataset, batch_size=4, shuffle=False)

    return data_loader

def load_trained_model() -> nn.Module:
    """Load the trained model."""
    
    model = SimpleNetwork()
    model.load_state_dict(torch.load('../data/trained_model.pth'))
    model.eval()
    
    return model

def create_robustness_plots(results: Dict[str, Any], epsilon_values: List[float]):
    """Create visualization plots for robustness results."""
    
    # Extract data for plotting
    epsilons = []
    fgsm_success = []
    pgd_success = []
    fgsm_robust = []
    pgd_robust = []
    fgsm_clean_acc = []
    pgd_clean_acc = []
    
    for eps in epsilon_values:
        eps_str = str(eps)
        if eps_str in results:
            epsilons.append(eps)
            fgsm_success.append(results[eps_str]['fgsm']['attack_success_rate'])
            pgd_success.append(results[eps_str]['pgd']['attack_success_rate'])
            fgsm_robust.append(results[eps_str]['fgsm']['robustness_rate'])
            pgd_robust.append(results[eps_str]['pgd']['robustness_rate'])
            fgsm_clean_acc.append(results[eps_str]['fgsm']['clean_accuracy'])
            pgd_clean_acc.append(results[eps_str]['pgd']['clean_accuracy'])
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    
    # Attack success rate plot
    ax1.plot(epsilons, fgsm_success, 'r-o', label='FGSM Attack Success', linewidth=2, markersize=8)
    ax1.plot(epsilons, pgd_success, 'b-s', label='PGD Attack Success', linewidth=2, markersize=8)
    ax1.set_xlabel('Perturbation Budget (ε)', fontsize=12)
    ax1.set_ylabel('Attack Success Rate (%)', fontsize=12)
    ax1.set_title('Attack Success Rate vs Perturbation Budget', fontsize=14, fontweight='bold')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.set_ylim(0, 100)
    
    # Robustness rate plot
    ax2.plot(epsilons, fgsm_robust, 'r-o', label='FGSM Robustness', linewidth=2, markersize=8)
    ax2.plot(epsilons, pgd_robust, 'b-s', label='PGD Robustness', linewidth=2, markersize=8)
    ax2.set_xlabel('Perturbation Budget (ε)', fontsize=12)
    ax2.set_ylabel('Robustness Rate (%)', fontsize=12)
    ax2.set_title('Model Robustness vs Perturbation Budget', fontsize=14, fontweight='bold')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.set_ylim(0, 100)
    
    # Clean accuracy consistency
    ax3.plot(epsilons, fgsm_clean_acc, 'g-o', label='Clean Accuracy', linewidth=2, markersize=8)
    ax3.axhline(y=np.mean(fgsm_clean_acc), color='g', linestyle='--', alpha=0.7, label=f'Mean: {np.mean(fgsm_clean_acc):.1f}%')
    ax3.set_xlabel('Perturbation Budget (ε)', fontsize=12)
    ax3.set_ylabel('Clean Accuracy (%)', fontsize=12)
    ax3.set_title('Clean Accuracy Consistency', fontsize=14, fontweight='bold')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    ax3.set_ylim(90, 100)
    
    # Adversarial accuracy comparison
    fgsm_adv_acc = [results[str(eps)]['fgsm']['adversarial_accuracy'] for eps in epsilons]
    pgd_adv_acc = [results[str(eps)]['pgd']['adversarial_accuracy'] for eps in epsilons]
    
    ax4.plot(epsilons, fgsm_adv_acc, 'r-o', label='FGSM Adversarial Accuracy', linewidth=2, markersize=8)
    ax4.plot(epsilons, pgd_adv_acc, 'b-s', label='PGD Adversarial Accuracy', linewidth=2, markersize=8)
    ax4.set_xlabel('Perturbation Budget (ε)', fontsize=12)
    ax4.set_ylabel('Adversarial Accuracy (%)', fontsize=12)
    ax4.set_title('Adversarial Accuracy vs Perturbation Budget', fontsize=14, fontweight='bold')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    ax4.set_ylim(0, 100)
    
    plt.tight_layout()
    plt.savefig('../plots/python_robustness_results.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("Robustness plots saved to ../plots/python_robustness_results.png")

def create_adversarial_examples_visualization(model: nn.Module, data_loader: DataLoader):
    """Create visualization of adversarial examples."""
    
    # Get first batch
    data, target = next(iter(data_loader))
    
    # Select first 5 samples
    data = data[:5]
    target = target[:5]
    
    # Generate adversarial examples with different epsilons
    epsilons = [0.0, 0.1, 0.2, 0.3]
    
    fig, axes = plt.subplots(len(epsilons), 5, figsize=(15, 12))
    fig.suptitle('Adversarial Examples with Different Perturbation Budgets', fontsize=16, fontweight='bold')
    
    for eps_idx, epsilon in enumerate(epsilons):
        if epsilon == 0.0:
            adv_data = data
            title_prefix = "Clean"
        else:
            adv_data = AdversarialAttacks.fgsm_attack(model, data, target, epsilon)
            title_prefix = f"ε={epsilon}"
        
        # Get predictions
        with torch.no_grad():
            output = model(adv_data)
            pred = output.argmax(dim=1)
            confidence = F.softmax(output, dim=1).max(dim=1)[0]
        
        for img_idx in range(min(5, len(adv_data))):
            ax = axes[eps_idx, img_idx]

            # Display image
            img = adv_data[img_idx].squeeze().detach().numpy()
            ax.imshow(img, cmap='gray')
            
            # Set title with prediction
            true_label = target[img_idx].item()
            pred_label = pred[img_idx].item()
            conf_score = confidence[img_idx].item()
            
            if pred_label == true_label:
                color = 'green'
                status = '✓'
            else:
                color = 'red' 
                status = '✗'
            
            ax.set_title(f'{title_prefix}\nTrue: {true_label}, Pred: {pred_label} {status}\nConf: {conf_score:.2f}', 
                        color=color, fontsize=9)
            ax.axis('off')
    
    plt.tight_layout()
    plt.savefig('../plots/adversarial_examples.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("Adversarial examples visualization saved to ../plots/adversarial_examples.png")

def save_results(results: Dict[str, Any], epsilon_values: List[float]):
    """Save robustness testing results."""
    
    # Save detailed results as JSON
    with open('../results/python_robustness_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    # Save summary as CSV
    with open('../results/python_robustness_summary.csv', 'w', newline='') as csvfile:
        writer = csv.writer(csvfile)
        
        # Header
        header = ['epsilon', 'attack_method', 'clean_accuracy', 'adversarial_accuracy', 
                 'attack_success_rate', 'robustness_rate', 'attack_success_count', 'total_samples',
                 'avg_perturbation_norm', 'max_perturbation_norm', 'avg_confidence_drop', 
                 'test_time_seconds']
        writer.writerow(header)
        
        # Data rows
        for eps in epsilon_values:
            eps_str = str(eps)
            if eps_str in results:
                for attack_method in ['fgsm', 'pgd']:
                    if attack_method in results[eps_str]:
                        result = results[eps_str][attack_method]
                        row = [
                            eps,
                            attack_method,
                            result['clean_accuracy'],
                            result['adversarial_accuracy'],
                            result['attack_success_rate'],
                            result['robustness_rate'],
                            result['attack_success_count'],
                            result['total_samples'],
                            result['avg_perturbation_norm'],
                            result['max_perturbation_norm'],
                            result['avg_confidence_drop'],
                            result['test_time_seconds']
                        ]
                        writer.writerow(row)
    
    # Save performance metrics
    performance_summary = {
        'total_epsilon_values_tested': len(epsilon_values),
        'total_attack_methods': 2,
        'total_test_time': sum(
            results[str(eps)][method]['test_time_seconds'] 
            for eps in epsilon_values if str(eps) in results
            for method in ['fgsm', 'pgd'] if method in results[str(eps)]
        ),
        'average_robustness_rates': {
            'fgsm': np.mean([results[str(eps)]['fgsm']['robustness_rate'] 
                           for eps in epsilon_values if str(eps) in results]),
            'pgd': np.mean([results[str(eps)]['pgd']['robustness_rate'] 
                          for eps in epsilon_values if str(eps) in results])
        },
        'experiment_timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
        'approach': 'empirical_testing',
        'guarantee_level': 'none'
    }
    
    with open('../results/python_performance_summary.json', 'w') as f:
        json.dump(performance_summary, f, indent=2)
    
    print("Results saved to ../results/python_robustness_results.json and ../results/python_robustness_summary.csv")
    print("Performance summary saved to ../results/python_performance_summary.json")

def main():
    """Main robustness evaluation pipeline."""
    
    print("=" * 80)
    print("ADVERSARIAL ROBUSTNESS VERIFICATION - PYTHON IMPLEMENTATION")
    print("=" * 80)
    
    # Check if data exists
    if not os.path.exists('../data/trained_model.pth'):
        print("Error: Trained model not found. Please run data_processing.py first.")
        return {'success': False, 'error': 'missing_model'}
    
    # Load model and data
    print("\n1. Loading trained model...")
    model = load_trained_model()
    print(f"   Model loaded with {sum(p.numel() for p in model.parameters())} parameters")
    
    print("\n2. Loading test data...")
    test_loader = load_test_data()
    print(f"   Test data loaded: {len(test_loader.dataset)} samples")
    
    # Initialize evaluator
    print("\n3. Initializing robustness evaluator...")
    evaluator = RobustnessEvaluator(model)
    
    # Test robustness across epsilon values
    print("\n4. Testing robustness across different perturbation budgets...")
    epsilon_values = [0.05, 0.1, 0.15, 0.2, 0.25, 0.3]
    results = evaluator.test_epsilon_range(test_loader, epsilon_values)
    
    # Create visualizations
    print("\n5. Creating visualizations...")
    create_robustness_plots(results, epsilon_values)
    create_adversarial_examples_visualization(model, test_loader)
    
    # Save results
    print("\n6. Saving results...")
    save_results(results, epsilon_values)
    
    # Print summary
    print("\n" + "=" * 80)
    print("PYTHON ROBUSTNESS TESTING COMPLETE")
    print("=" * 80)
    
    # Summary statistics
    eps_01_results = results.get('0.1', {})
    if 'fgsm' in eps_01_results:
        fgsm_result = eps_01_results['fgsm']
        print(f"Summary for ε=0.1:")
        print(f"  FGSM Attack Success Rate: {fgsm_result['attack_success_rate']:.2f}%")
        print(f"  FGSM Robustness Rate: {fgsm_result['robustness_rate']:.2f}%")
        
    if 'pgd' in eps_01_results:
        pgd_result = eps_01_results['pgd']
        print(f"  PGD Attack Success Rate: {pgd_result['attack_success_rate']:.2f}%")
        print(f"  PGD Robustness Rate: {pgd_result['robustness_rate']:.2f}%")
    
    print("\nApproach: Empirical testing with no formal guarantees")
    print("Method: Gradient-based adversarial attacks (FGSM, PGD)")
    print("Limitation: Only tests specific attack methods, no universal robustness proof")
    
    return {
        'success': True,
        'results': results,
        'epsilon_values': epsilon_values,
        'summary': {
            'total_tests': len(epsilon_values) * 2,  # 2 attack methods
            'approach': 'empirical_testing'
        }
    }

if __name__ == "__main__":
    results = main()
    
    # Save experiment results for comparison stage
    with open('../results/python_experiment_results.json', 'w') as f:
        json.dump(results, f, indent=2)