#!/usr/bin/env python3
"""
Data Processing for Adversarial Robustness Verification
========================================================

Prepares MNIST dataset and trains a simple neural network for robustness testing.
Creates standardized data files for both Python and Idris implementations.

Dataset: MNIST (subset for faster experiments)
Architecture: [784 -> 256 -> 128 -> 10] fully connected with ReLU
"""

import os
import json
import time
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import DataLoader, Subset
from torchvision import datasets, transforms
import matplotlib.pyplot as plt
from typing import Tuple, Dict, Any
import csv

# Create necessary directories
os.makedirs('../data', exist_ok=True)
os.makedirs('../results', exist_ok=True)
os.makedirs('../plots', exist_ok=True)

class SimpleNetwork(nn.Module):
    """Simple feedforward neural network for MNIST classification."""
    
    def __init__(self):
        super(SimpleNetwork, self).__init__()
        self.fc1 = nn.Linear(784, 256)
        self.fc2 = nn.Linear(256, 128) 
        self.fc3 = nn.Linear(128, 10)
        
    def forward(self, x):
        x = x.view(-1, 784)  # Flatten
        x = F.relu(self.fc1(x))
        x = F.relu(self.fc2(x))
        x = self.fc3(x)
        return x

def load_mnist_data(subset_size: int = 10000, test_size: int = 1000) -> Tuple[DataLoader, DataLoader]:
    """Load MNIST dataset with subset for faster experiments."""
    
    transform = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize((0.1307,), (0.3081,))  # MNIST normalization
    ])
    
    # Load full datasets
    train_dataset = datasets.MNIST('../data', train=True, download=True, transform=transform)
    test_dataset = datasets.MNIST('../data', train=False, transform=transform)
    
    # Create subsets for faster experiments
    train_subset = Subset(train_dataset, range(subset_size))
    test_subset = Subset(test_dataset, range(test_size))
    
    train_loader = DataLoader(train_subset, batch_size=64, shuffle=True)
    test_loader = DataLoader(test_subset, batch_size=64, shuffle=False)
    
    return train_loader, test_loader

def train_network(model: nn.Module, train_loader: DataLoader, epochs: int = 5) -> Dict[str, Any]:
    """Train the neural network and return training metrics."""
    
    optimizer = optim.Adam(model.parameters(), lr=0.001)
    criterion = nn.CrossEntropyLoss()
    
    training_losses = []
    training_accuracies = []
    epoch_times = []
    
    print(f"Training network for {epochs} epochs...")
    total_start_time = time.time()
    
    for epoch in range(epochs):
        epoch_start_time = time.time()
        epoch_loss = 0.0
        correct = 0
        total = 0
        
        model.train()
        for batch_idx, (data, target) in enumerate(train_loader):
            optimizer.zero_grad()
            output = model(data)
            loss = criterion(output, target)
            loss.backward()
            optimizer.step()
            
            epoch_loss += loss.item()
            pred = output.argmax(dim=1, keepdim=True)
            correct += pred.eq(target.view_as(pred)).sum().item()
            total += target.size(0)
            
            if batch_idx % 50 == 0:
                print(f'Epoch {epoch+1}/{epochs}, Batch {batch_idx}/{len(train_loader)}, '
                      f'Loss: {loss.item():.4f}')
        
        epoch_time = time.time() - epoch_start_time
        avg_loss = epoch_loss / len(train_loader)
        accuracy = 100. * correct / total
        
        training_losses.append(avg_loss)
        training_accuracies.append(accuracy)
        epoch_times.append(epoch_time)
        
        print(f'Epoch {epoch+1}: Loss: {avg_loss:.4f}, Accuracy: {accuracy:.2f}%, Time: {epoch_time:.1f}s')
    
    total_training_time = time.time() - total_start_time
    
    return {
        'training_time': total_training_time,
        'epoch_times': epoch_times,
        'losses': training_losses,
        'accuracies': training_accuracies,
        'final_accuracy': training_accuracies[-1],
        'epochs': epochs,
        'total_batches': len(train_loader) * epochs
    }

def evaluate_network(model: nn.Module, test_loader: DataLoader) -> Dict[str, Any]:
    """Evaluate network accuracy on test set."""
    
    model.eval()
    correct = 0
    total = 0
    class_correct = [0] * 10
    class_total = [0] * 10
    
    eval_start_time = time.time()
    
    with torch.no_grad():
        for data, target in test_loader:
            output = model(data)
            pred = output.argmax(dim=1, keepdim=True)
            correct += pred.eq(target.view_as(pred)).sum().item()
            total += target.size(0)
            
            # Per-class accuracy
            for i in range(target.size(0)):
                label = target[i].item()
                class_correct[label] += (pred[i] == label).item()
                class_total[label] += 1
    
    eval_time = time.time() - eval_start_time
    accuracy = 100. * correct / total
    
    # Per-class accuracies
    class_accuracies = {}
    for i in range(10):
        if class_total[i] > 0:
            class_accuracies[f'class_{i}'] = 100. * class_correct[i] / class_total[i]
        else:
            class_accuracies[f'class_{i}'] = 0.0
    
    print(f'Test Accuracy: {accuracy:.2f}%')
    
    return {
        'test_accuracy': accuracy,
        'correct_predictions': correct,
        'total_samples': total,
        'evaluation_time': eval_time,
        'class_accuracies': class_accuracies
    }

def save_model_weights(model: nn.Module, filepath: str):
    """Save model weights in both PyTorch and JSON formats."""
    
    # Save PyTorch state dict
    torch.save(model.state_dict(), filepath + '.pth')
    
    # Save weights as JSON for Idris consumption
    weights_dict = {}
    for name, param in model.named_parameters():
        weights_dict[name] = param.data.numpy().tolist()
    
    with open(filepath + '.json', 'w') as f:
        json.dump(weights_dict, f, indent=2)
    
    # Save model architecture info
    architecture_info = {
        'layer_sizes': [784, 256, 128, 10],
        'activation': 'relu',
        'num_parameters': sum(p.numel() for p in model.parameters()),
        'trainable_parameters': sum(p.numel() for p in model.parameters() if p.requires_grad)
    }
    
    with open(filepath + '_architecture.json', 'w') as f:
        json.dump(architecture_info, f, indent=2)
    
    print(f"Model weights saved to {filepath}.pth and {filepath}.json")
    print(f"Architecture info saved to {filepath}_architecture.json")

def save_test_samples(test_loader: DataLoader, num_samples: int = 100):
    """Save test samples for robustness testing."""
    
    model_eval_data = []
    samples_collected = 0
    
    for data, target in test_loader:
        batch_size = data.size(0)
        
        for i in range(min(batch_size, num_samples - samples_collected)):
            sample = {
                'sample_id': samples_collected,
                'image': data[i].numpy().tolist(),
                'label': int(target[i].item()),
                'flattened': data[i].view(-1).numpy().tolist(),
                'shape': list(data[i].shape)
            }
            model_eval_data.append(sample)
            samples_collected += 1
            
            if samples_collected >= num_samples:
                break
        
        if samples_collected >= num_samples:
            break
    
    # Save as JSON
    with open('../data/test_samples.json', 'w') as f:
        json.dump(model_eval_data, f, indent=2)
    
    # Save as CSV for easier Idris consumption
    with open('../data/test_samples.csv', 'w', newline='') as csvfile:
        writer = csv.writer(csvfile)
        
        # Header
        header = ['sample_id', 'label'] + [f'pixel_{i}' for i in range(784)]
        writer.writerow(header)
        
        # Data rows
        for sample in model_eval_data:
            row = [sample['sample_id'], sample['label']] + sample['flattened']
            writer.writerow(row)
    
    # Save sample statistics
    sample_stats = {
        'total_samples': len(model_eval_data),
        'samples_per_class': {},
        'mean_pixel_value': np.mean([np.mean(sample['flattened']) for sample in model_eval_data]),
        'std_pixel_value': np.std([np.mean(sample['flattened']) for sample in model_eval_data])
    }
    
    # Count samples per class
    for i in range(10):
        sample_stats['samples_per_class'][f'class_{i}'] = sum(1 for sample in model_eval_data if sample['label'] == i)
    
    with open('../data/test_samples_stats.json', 'w') as f:
        json.dump(sample_stats, f, indent=2)
    
    print(f"Saved {len(model_eval_data)} test samples to ../data/test_samples.json and ../data/test_samples.csv")

def create_training_plots(metrics: Dict[str, Any]):
    """Create visualization plots for training metrics."""
    
    epochs = range(1, len(metrics['losses']) + 1)
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    
    # Loss plot
    ax1.plot(epochs, metrics['losses'], 'b-', label='Training Loss', linewidth=2)
    ax1.set_title('Training Loss Over Epochs', fontsize=14, fontweight='bold')
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Loss')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Accuracy plot
    ax2.plot(epochs, metrics['accuracies'], 'r-', label='Training Accuracy', linewidth=2)
    ax2.set_title('Training Accuracy Over Epochs', fontsize=14, fontweight='bold')
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('Accuracy (%)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # Time per epoch
    ax3.bar(epochs, metrics['epoch_times'], color='green', alpha=0.7)
    ax3.set_title('Training Time Per Epoch', fontsize=14, fontweight='bold')
    ax3.set_xlabel('Epoch')
    ax3.set_ylabel('Time (seconds)')
    ax3.grid(True, alpha=0.3)
    
    # Training summary
    ax4.text(0.1, 0.9, f"Total Training Time: {metrics['training_time']:.1f}s", transform=ax4.transAxes, fontsize=12)
    ax4.text(0.1, 0.8, f"Final Accuracy: {metrics['final_accuracy']:.2f}%", transform=ax4.transAxes, fontsize=12)
    ax4.text(0.1, 0.7, f"Total Epochs: {metrics['epochs']}", transform=ax4.transAxes, fontsize=12)
    ax4.text(0.1, 0.6, f"Total Batches: {metrics['total_batches']}", transform=ax4.transAxes, fontsize=12)
    ax4.text(0.1, 0.5, f"Avg Time/Epoch: {np.mean(metrics['epoch_times']):.1f}s", transform=ax4.transAxes, fontsize=12)
    ax4.set_title('Training Summary', fontsize=14, fontweight='bold')
    ax4.set_xlim(0, 1)
    ax4.set_ylim(0, 1)
    ax4.axis('off')
    
    plt.tight_layout()
    plt.savefig('../plots/training_metrics.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("Training plots saved to ../plots/training_metrics.png")

def visualize_sample_data():
    """Create visualization of sample MNIST digits."""
    
    transform = transforms.Compose([transforms.ToTensor()])
    dataset = datasets.MNIST('../data', train=False, download=False, transform=transform)
    
    fig, axes = plt.subplots(2, 5, figsize=(12, 6))
    fig.suptitle('Sample MNIST Digits for Robustness Testing', fontsize=16, fontweight='bold')
    
    for i in range(10):
        image, label = dataset[i]
        row, col = i // 5, i % 5
        
        axes[row, col].imshow(image.squeeze(), cmap='gray')
        axes[row, col].set_title(f'Label: {label}', fontsize=12, fontweight='bold')
        axes[row, col].axis('off')
    
    plt.tight_layout()
    plt.savefig('../plots/sample_digits.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("Sample digits visualization saved to ../plots/sample_digits.png")

def save_experiment_metadata(training_metrics: Dict[str, Any], test_metrics: Dict[str, Any]):
    """Save experiment metadata for comparison."""
    
    metadata = {
        'experiment_info': {
            'dataset': 'MNIST',
            'architecture': '[784, 256, 128, 10]',
            'training_samples': 10000,
            'test_samples': 1000,
            'framework': 'PyTorch',
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        },
        'training_results': {
            'epochs': training_metrics['epochs'],
            'final_training_accuracy': training_metrics['final_accuracy'],
            'training_time_seconds': training_metrics['training_time'],
            'average_epoch_time': np.mean(training_metrics['epoch_times']),
            'final_loss': training_metrics['losses'][-1]
        },
        'test_results': {
            'test_accuracy': test_metrics['test_accuracy'],
            'evaluation_time_seconds': test_metrics['evaluation_time'],
            'correct_predictions': test_metrics['correct_predictions'],
            'total_test_samples': test_metrics['total_samples'],
            'class_accuracies': test_metrics['class_accuracies']
        },
        'model_info': {
            'total_parameters': None,  # Will be filled when model is saved
            'model_size_mb': None,
            'architecture_depth': 3,
            'activation_function': 'relu'
        }
    }
    
    with open('../data/experiment_metadata.json', 'w') as f:
        json.dump(metadata, f, indent=2)
    
    print("Experiment metadata saved to ../data/experiment_metadata.json")
    return metadata

def main():
    """Main data processing pipeline."""
    
    print("=" * 70)
    print("ADVERSARIAL ROBUSTNESS VERIFICATION - DATA PROCESSING")
    print("=" * 70)
    
    # Set random seeds for reproducibility
    torch.manual_seed(42)
    np.random.seed(42)
    
    # Load data
    print("\n1. Loading MNIST data...")
    train_loader, test_loader = load_mnist_data()
    print(f"   Training batches: {len(train_loader)}")
    print(f"   Test batches: {len(test_loader)}")
    
    # Create and train model
    print("\n2. Creating neural network...")
    model = SimpleNetwork()
    print(f"   Model architecture: {model}")
    print(f"   Total parameters: {sum(p.numel() for p in model.parameters())}")
    
    print("\n3. Training network...")
    training_metrics = train_network(model, train_loader)
    
    # Evaluate model
    print("\n4. Evaluating network...")
    test_metrics = evaluate_network(model, test_loader)
    
    # Save model and data
    print("\n5. Saving model weights...")
    save_model_weights(model, '../data/trained_model')
    
    print("\n6. Saving test samples...")
    save_test_samples(test_loader)
    
    # Create visualizations
    print("\n7. Creating visualizations...")
    create_training_plots(training_metrics)
    visualize_sample_data()
    
    # Save metadata
    print("\n8. Saving experiment metadata...")
    metadata = save_experiment_metadata(training_metrics, test_metrics)
    
    print("\n" + "=" * 70)
    print("DATA PROCESSING COMPLETE")
    print("=" * 70)
    print(f"Final test accuracy: {test_metrics['test_accuracy']:.2f}%")
    print(f"Training time: {training_metrics['training_time']:.2f} seconds")
    print(f"Model parameters: {sum(p.numel() for p in model.parameters())}")
    print("\nFiles created:")
    print("- ../data/trained_model.pth (PyTorch weights)")
    print("- ../data/trained_model.json (JSON weights for Idris)")
    print("- ../data/trained_model_architecture.json (Architecture info)")
    print("- ../data/test_samples.json (Test samples)")
    print("- ../data/test_samples.csv (Test samples for Idris)")
    print("- ../data/test_samples_stats.json (Sample statistics)")
    print("- ../data/experiment_metadata.json (Experiment info)")
    print("- ../plots/training_metrics.png (Training plots)")
    print("- ../plots/sample_digits.png (Sample visualization)")
    
    # Return results for chaining
    return {
        'success': True,
        'training_metrics': training_metrics,
        'test_metrics': test_metrics,
        'metadata': metadata
    }

if __name__ == "__main__":
    results = main()
    
    # Save processing results for next stage
    with open('../results/data_processing_results.json', 'w') as f:
        json.dump(results, f, indent=2)