# Adversarial Robustness Verification: <PERSON><PERSON><PERSON> vs Python

This project implements **Project 5** from the dissertation "Accelerated Machine Learning with Dependent Types", comparing formal verification approaches (Idris with dependent types) against empirical testing approaches (Python with PyTorch) for adversarial robustness in neural networks.

## 🎯 Project Overview

**Goal**: Compare formal mathematical proofs of adversarial robustness (Idris) vs empirical adversarial attack testing (Python)

**Research Question**: Can dependent types provide stronger robustness guarantees than empirical testing, and at what cost in terms of complexity and scalability?

## 📊 Experimental Setup

### Dataset
- **MNIST** handwritten digit classification
- Subset: 10,000 training samples, 1,000 test samples
- Architecture: Simple feedforward network [784 → 256 → 128 → 10]

### Approaches Compared

#### 🔬 **Idris Approach: Formal Verification**
- **Method**: Mathematical proofs using dependent types
- **Property**: `ε-Robust : (eps : Double) -> (Model -> Type)`
- **Guarantee**: Compile-time verification that perturbations ≤ ε don't change classification
- **Limitation**: Simplified to linear models for tractable verification

#### 🐍 **Python Approach: Empirical Testing**
- **Method**: Adversarial attacks (FGSM, PGD, Random noise)
- **Testing**: Multiple perturbation budgets (ε = 0.05 to 0.3)
- **Metrics**: Attack success rates, robustness percentages
- **Limitation**: No formal guarantees, only confidence from testing

## 🛠️ System Requirements

### Hardware
- **Intel i7 CPU** with 16GB RAM (as specified for experimental consistency)
- **macOS** compatible (Intel)

### Software Dependencies

#### Python Environment
```bash
# Required packages (automatically installed)
pip install torch torchvision numpy matplotlib seaborn pandas
```

#### Idris Environment
```bash
# Install pack (Idris package manager)
# Follow instructions at: https://github.com/stefan-hoeck/idris2-pack

# Install Spidr tensor library (for complete Idris functionality)
# Note: Spidr is experimental and may not be fully available
```

## 🚀 Quick Start

### 1. Clone and Setup
```bash
git clone <repository-url>
cd adversarial-robustness-verification
chmod +x run_experiment.sh
```

### 2. Run Complete Experiment
```bash
# Run complete experiment (recommended)
./run_experiment.sh

# Or run specific components
./run_experiment.sh --python-only    # Python empirical testing only
./run_experiment.sh --idris-only     # Idris formal verification only
./run_experiment.sh --skip-data      # Skip data processing step
./run_experiment.sh --quick          # Quick experiment with reduced data
```

### 3. Manual Execution (Alternative)
```bash
# Step-by-step execution
cd python
python3 data_processing.py              # Prepare data and train model
python3 adversarial_robustness.py       # Python empirical testing
python3 generate_comparison_plots.py    # Generate comparison plots
cd ..
pack build adversarial_robustness       # Build Idris project
pack exec adversarial_robustness        # Run Idris verification
```

## 📁 Project Structure

```
adversarial-robustness-verification/
├── python/                         # Python implementation
│   ├── data_processing.py          #   MNIST data prep & model training
│   ├── adversarial_robustness.py   #   Empirical robustness testing
│   └── generate_comparison_plots.py #   Comparison visualizations
├── src/
│   └── AdversarialRobustness.idr   # Idris formal verification
├── adversarial_robustness.ipkg     # Idris package configuration
├── pack.toml                       # Pack package manager config
├── run_experiment.sh               # Automated experiment runner
├── requirements.txt                # Python dependencies
├── config.json                     # Experiment configuration
├── README.md                       # This file
├── data/                          # Generated data files
│   ├── trained_model.pth          #   PyTorch model weights
│   ├── trained_model.json         #   JSON weights for Idris
│   ├── test_samples.csv           #   Test samples for verification
│   ├── test_samples_stats.json    #   Sample statistics
│   └── experiment_metadata.json   #   Experiment configuration
├── results/                       # Experimental results
│   ├── python_robustness_results.json    #   Detailed Python results
│   ├── python_robustness_summary.csv     #   Python summary metrics
│   ├── python_performance_summary.json   #   Python performance data
│   ├── idris_timing.json                 #   Idris execution metrics
│   ├── comparison_report.txt             #   Comparison analysis
│   └── final_experiment_report.txt       #   Complete experiment summary
└── plots/                         # Visualizations
    ├── training_metrics.png       #   Model training curves
    ├── sample_digits.png          #   MNIST sample visualization
    ├── python_robustness_results.png     #   Robustness vs epsilon plots
    ├── adversarial_examples.png          #   Example adversarial images
    ├── approach_comparison_radar.png     #   Radar chart comparison
    ├── verification_strength_comparison.png  #   Verification analysis
    ├── performance_comparison.png        #   Development effort comparison
    ├── results_summary_table.png         #   Summary table
    ├── trade_off_visualization.png       #   Trade-off analysis
    └── robustness_summary.png           #   Robustness summary
```

## 📈 Evaluation Metrics

### 1. **Correctness & Verification Strength**
- **Python**: Attack success rates, empirical robustness percentages
- **Idris**: Formal proof existence, compile-time guarantees

### 2. **Development Complexity**  
- **Lines of Code**: Implementation size comparison
- **Cognitive Load**: Type annotations vs proof construction
- **Learning Curve**: Time to become productive

### 3. **Performance Metrics**
- **Execution Time**: Runtime for verification/testing
- **Compilation Time**: Idris type-checking overhead
- **Scalability**: Maximum model/dataset size handled

### 4. **Practical Usability**
- **Tooling**: IDE support, debugging capabilities
- **Ecosystem**: Available libraries and resources
- **Integration**: Compatibility with existing workflows

## 📊 Understanding Results

### Python Results Interpretation
```csv
epsilon,attack_method,clean_accuracy,adversarial_accuracy,attack_success_rate,robustness_rate
0.1,fgsm,95.2,78.3,16.9,83.1
0.1,pgd,95.2,72.1,23.1,76.9
```

- **Attack Success Rate**: % of clean correct samples that become wrong after attack
- **Robustness Rate**: % of samples that maintain correct classification
- **Higher ε**: Larger perturbation budget, typically lower robustness

### Idris Results Interpretation
```
✓ Linear robustness theorem verified
Sample 0: Robustness verified for this input
Sample 1: Robustness verified for this input
...
Robustness rate: 100.0%
```

- **Theorem Verified**: Mathematical proof of robustness property exists
- **Per-sample Verification**: Individual robustness checks
- **100% Rate**: Formal guarantee (when proof exists) vs empirical confidence

## 🔍 Key Insights

### **Strengths & Limitations**

#### Idris Formal Verification
✅ **Strengths**:
- Mathematical certainty when proofs exist
- No false negatives (if robust, guaranteed robust)
- Compile-time error prevention
- Principled approach to safety-critical systems

❌ **Limitations**:
- Limited to simplified models for tractable verification
- Requires mathematical expertise
- Long compilation times for complex proofs
- Incomplete ecosystem compared to Python

#### Python Empirical Testing
✅ **Strengths**:
- Works with full-scale neural networks
- Easy to implement and understand
- Rich ecosystem of attack methods
- Practical for real-world applications

❌ **Limitations**:
- No formal guarantees
- May miss attack methods not tested
- False sense of security
- Computationally expensive for comprehensive testing

### **Trade-off Analysis**

| Aspect | Idris (Formal) | Python (Empirical) |
|--------|----------------|-------------------|
| **Verification Strength** | 🟢 Mathematical proof | 🟡 Empirical confidence |
| **Model Complexity** | 🔴 Simple models only | 🟢 Full neural networks |
| **Implementation Effort** | 🔴 High (proof construction) | 🟢 Low (standard attacks) |
| **Scalability** | 🔴 Limited | 🟢 Good |
| **Certainty** | 🟢 100% when proof exists | 🟡 Depends on test coverage |
| **Real-world Usage** | 🔴 Research/critical systems | 🟢 Industry standard |

## 🎓 Educational Value

This project demonstrates:

1. **Dependent Type Benefits**: How types can encode and verify mathematical properties
2. **Verification Challenges**: Why formal verification is hard for complex ML models  
3. **Practical Trade-offs**: Balancing verification strength vs implementation complexity
4. **Research Frontiers**: Current limitations and future directions in formal ML verification

## 🔧 Troubleshooting

### Common Issues

#### Python Dependencies
```bash
# If automatic installation fails
pip install -r requirements.txt

# For M1 Macs, ensure compatible PyTorch version
pip install torch torchvision --index-url https://download.pytorch.org/whl/cpu
```

#### Idris Build Failures
```bash
# If pack is not installed
# Visit: https://github.com/stefan-hoeck/idris2-pack

# If tensor library is missing
# Spidr tensor library is experimental
# The project will run with simplified verification demonstrations
```

#### Permission Issues
```bash
chmod +x run_experiment.sh  # Make script executable
```

#### Memory Issues
```bash
# For systems with limited memory, use quick mode
./run_experiment.sh --quick
```

## 📊 Expected Outputs

After running the complete experiment, you'll find:

### **Data Directory** (`data/`)
- Trained neural network model (PyTorch format)
- Test samples for robustness evaluation
- Experiment metadata and statistics

### **Results Directory** (`results/`)
- Detailed robustness testing results (JSON/CSV)
- Performance metrics and timing data
- Comprehensive comparison reports

### **Plots Directory** (`plots/`)
- Training progress visualizations
- Robustness vs perturbation budget curves
- Approach comparison charts
- Trade-off analysis visualizations

### **Key Metrics Generated**
- **Python**: Attack success rates across different ε values
- **Idris**: Theoretical robustness guarantees (when applicable)
- **Comparison**: Development effort, verification strength, scalability

## 📚 Further Reading

- **Idris Documentation**: [Idris Tutorial](https://idris2.readthedocs.io/)
- **Dependent Types in ML**: Urban & Miné (2021) - Formal Methods for ML Review
- **Adversarial Robustness**: Goodfellow et al. - Explaining and Harnessing Adversarial Examples
- **Spidr Library**: [Spidr GitHub](https://github.com/spidr-org/spidr) (experimental)

## 📄 Citation

```bibtex
@misc{patil2025adversarial,
  title={Adversarial Robustness Verification: Formal Proofs vs Empirical Testing},
  author={Patil, Rishabh Ashok},
  year={2025},
  note={University of St Andrews - Accelerated ML with Dependent Types Project}
}
```

## 📞 Contact

**Rishabh Ashok Patil**  
Student ID: 230029895  
University of St Andrews  
Supervisor: Edwin Brady  
Secondary Supervisor: Thomas Hansen

---

## 🎯 Quick Commands Reference

```bash
# Complete experiment
./run_experiment.sh

# Python only
./run_experiment.sh --python-only

# Skip data processing (if already run)
./run_experiment.sh --skip-data

# Quick test with smaller dataset
./run_experiment.sh --quick

# Check results
ls -la results/
ls -la plots/

# View final report
cat results/final_experiment_report.txt
```

*This project is part of the dissertation "Accelerated Machine Learning with Dependent Types" exploring the practical benefits and limitations of dependent type systems in machine learning development.*